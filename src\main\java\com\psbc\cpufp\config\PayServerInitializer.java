package com.psbc.cpufp.config;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PayServerInitializer extends ChannelInitializer<SocketChannel> {

    private final PayServerHandler payServerHandler;

    @Override
    protected void initChannel(SocketChannel ch) {
        ch.pipeline()
                // 自定义长度字段解码器，处理6位长度前缀
                .addLast(new CustomLengthFieldDecoder())
                // 自定义长度字段编码器，添加6位长度前缀
                .addLast(new CustomLengthFieldEncoder())
                // 业务处理器
                .addLast(payServerHandler);
    }
}