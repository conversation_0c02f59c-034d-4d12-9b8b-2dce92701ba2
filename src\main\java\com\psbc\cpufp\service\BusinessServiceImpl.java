package com.psbc.cpufp.service;

import com.alibaba.fastjson.JSONObject;
import com.psbc.cpufp.util.BusinessUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 业务服务实现类
 * 迁移原 BusUtil 和 BusinessService 的核心业务逻辑
 * 处理发送列表数据请求（原btp12358端口）的所有逻辑
 */
@Service
public class BusinessServiceImpl {
    
    private static final Logger logger = LoggerFactory.getLogger(BusinessServiceImpl.class);
    
    @Value("${business.target.url:http://127.0.0.1:9702}")
    private String targetUrl;
    
    @Value("${business.target.timeout:30000}")
    private int timeout;
    
    private final RestTemplate restTemplate;
    
    public BusinessServiceImpl() {
        this.restTemplate = new RestTemplate();
    }
    
    /**
     * 发送列表数据请求（原btp12358端口）
     * 对应原 BusUtil.list 方法
     */
    public Map<String, Object> list(Map<String, Object> criteria) {
        logger.info("开始处理列表数据请求（原btp12358端口）: {}", criteria);
        return processBusinessRequest(criteria, "list");
    }
    
    /**
     * 发送实时代收付数据请求
     * 对应原 BusUtil.list1 方法
     */
    public Map<String, Object> list1(Map<String, Object> criteria) {
        logger.info("开始处理实时代收付数据请求: {}", criteria);
        return processBusinessRequest(criteria, "list1");
    }
    
    /**
     * 发送普通接口返回报文
     * 对应原 BusUtil.ordin 方法
     */
    public Map<String, Object> ordin(Map<String, Object> criteria) {
        logger.info("开始处理普通接口请求: {}", criteria);
        return processBusinessRequest(criteria, "ordin");
    }
    
    /**
     * 发送普通接口返回报文1
     * 对应原 BusUtil.ordin1 方法
     */
    public Map<String, Object> ordin1(Map<String, Object> criteria) {
        logger.info("开始处理普通接口请求1: {}", criteria);
        return processBusinessRequest(criteria, "ordin1");
    }
    
    /**
     * 发送普通接口返回报文2
     * 对应原 BusUtil.ordin2 方法
     */
    public Map<String, Object> ordin2(Map<String, Object> criteria) {
        logger.info("开始处理普通接口请求2: {}", criteria);
        return processBusinessRequest(criteria, "ordin2");
    }
    
    /**
     * 核心业务请求处理方法
     * 整合原 BusUtil.send 方法的逻辑
     */
    private Map<String, Object> processBusinessRequest(Map<String, Object> criteria, String method) {
        try {
            // 组装请求报文
            Map<String, Object> requestMessage = buildRequestMessage(criteria, method);
            
            // 检查报文是否有缺失字段
            if (!validateRequestMessage(requestMessage)) {
                String code = (String) requestMessage.get("code");
                String msg = (String) requestMessage.get("msg");
                
                // 获取用户信息用于日志
                String empname = (String) criteria.get("empname");
                String orgcode = (String) criteria.get("orgcode");
                
                String errorMsg = "报文中【" + msg + "】数据为空。";
                logger.warn("发起人:{}||机构：{}||{}", empname, orgcode, createErrorResponse(errorMsg));
                return createErrorResponse(errorMsg);
            }
            
            // 移除验证字段
            requestMessage.remove("code");
            requestMessage.remove("msg");
            
            // 记录发送报文日志
            String empname = (String) criteria.get("empname");
            String orgcode = (String) criteria.get("orgcode");
            String sendseqno = getNestedValue(requestMessage, "head.sendseqno");
            
            logger.info("发送报文：{}", JSONObject.toJSONString(requestMessage));
            logger.warn("发起人:{}||机构：{}||发送报文：{}", empname, orgcode, JSONObject.toJSONString(requestMessage));
            
            // 发送HTTP请求
            String response = sendHttpRequest(requestMessage, sendseqno, empname, orgcode,(String) criteria.get("targetUrl"));
            
            // 记录返回报文日志
            logger.info("返回报文：{}", response);
            logger.warn("发起人:{}||机构：{}||返回报文：{}", empname, orgcode, response);
            
            // 解析响应
            return parseResponse(response, empname, orgcode);
            
        } catch (Exception e) {
            logger.error("处理业务请求失败, method: {}, criteria: {}", method, criteria, e);
            
            // 获取用户信息用于日志
            String empname = (String) criteria.get("empname");
            String orgcode = (String) criteria.get("orgcode");
            
            logger.warn("发起人:{}||机构：{}||{}", empname, orgcode, createErrorResponse("请求超时"));
            return createErrorResponse("请求超时");
        }
    }
    
    /**
     * 组装请求报文
     * 对应原 BusUtil.getHead 和 getMerchHead 方法
     */
    private Map<String, Object> buildRequestMessage(Map<String, Object> criteria, String method) {
        Map<String, Object> message = new HashMap<>();
        
        // 组装head部分
        Map<String, Object> head = new HashMap<>();
        
        // 初始化head字段
        head.put("merchid", "");
        head.put("opecd", "");
        head.put("busikind", "");
        head.put("tradecode", "");
        head.put("sendseqno", "");
        head.put("empname", "");
        head.put("empcode", "");
        head.put("orgcode", "");
        head.put("orgdegree", "");
        
        // 组装body部分
        Map<String, Object> body = new HashMap<>();
        
        // 检查缺失字段
        String missingField = "";
        
        // 处理criteria中的参数
        for (Map.Entry<String, Object> entry : criteria.entrySet()) {
            String key = entry.getKey().toLowerCase();
            String value = entry.getValue() == null ? "" : entry.getValue().toString();
            
            if ("merchid".equals(key)) {
                // 委托单位代码
                if (BusinessUtil.isEmpty(value)) {
                    head.put("merchid", "");
                } else {
                    head.put("merchid", value);
                    body.put("merchid", value); // 同时加入body
                }
            } else if ("opecd".equals(key)) {
                // 业务代码
                if (BusinessUtil.isEmpty(value)) {
                    head.put("opecd", "");
                } else {
                    head.put("opecd", value);
                    body.put("opecd", value); // 同时加入body
                }
            } else if ("busikind".equals(key)) {
                // 业务类型
                if (BusinessUtil.isEmpty(value)) {
                    missingField = key;
                } else {
                    head.put("busikind", value);
                }
            } else if ("tradecode".equals(key)) {
                // 交易代码
                if (BusinessUtil.isEmpty(value)) {
                    missingField = key;
                } else {
                    head.put("tradecode", value);
                }
            } else if ("empname".equals(key)) {
                // 发送用户名称
                if (BusinessUtil.isEmpty(value)) {
                    missingField = key;
                } else {
                    head.put("empname", value);
                    body.put("empname", value);
                }
            } else if ("empcode".equals(key)) {
                // 发送用户账号
                if (BusinessUtil.isEmpty(value)) {
                    missingField = key;
                } else {
                    head.put("empcode", value);
                    body.put("empcode", value);
                }
            } else if ("orgcode".equals(key)) {
                // 发送机构
                if (BusinessUtil.isEmpty(value)) {
                    missingField = key;
                } else {
                    head.put("orgcode", value);
                    body.put("orgcode", value);
                }
            } else if ("orgdegree".equals(key)) {
                // 机构级别
                if (BusinessUtil.isEmpty(value)) {
                    missingField = key;
                } else {
                    head.put("orgdegree", value);
                    body.put("orgdegree", value);
                }
            } else if ("targeturl".equals(key) || "action".equals(key)){
                    //跳过

            }
            else {
                // 其他字段放入body
                body.put(key.toLowerCase(), value);
            }
        }

        // 生成sendseqno
        String sendseqno;
        String busikind = (String) head.get("busikind");
        String tradecode = (String) head.get("tradecode");
        
        // 特殊处理XMSS情况
        if ("XMSS".equals(criteria.get("str23"))) {
            String dateTime = BusinessUtil.getCurrentShortTime();
            sendseqno = dateTime.length() >= 14 ? dateTime.substring(8, 14) : dateTime;
        } else {
            sendseqno = busikind + "_" + tradecode + "_" + BusinessUtil.getCurrentShortTime();
        }
        
        head.put("sendseqno", sendseqno);
        
        // 检查是否有缺失字段
        if (!BusinessUtil.isEmpty(missingField)) {
            message.put("code", "9998");
            message.put("msg", missingField);
        } else {
            message.put("head", head);
            message.put("body", body);
            message.put("code", "0000");
            message.put("msg", "");
        }

        logger.debug("组装的请求报文: {}", message);
        return message;
    }
    
    /**
     * 发送HTTP请求
     * 对应原 BusUtil.send 方法中的HTTP发送逻辑
     */
    private String sendHttpRequest(Map<String, Object> requestMessage, String sendseqno, String empname, String orgcode,String targetUrl) {
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 转换为JSON字符串
            String jsonRequest = JSONObject.toJSONString(requestMessage);
            logger.debug("发送请求到目标服务: {}, 请求内容: {}", targetUrl, jsonRequest);
            
            HttpEntity<String> entity = new HttpEntity<>(jsonRequest, headers);
            
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(
                targetUrl, entity, String.class);
            
            String responseBody = response.getBody();
            
            return responseBody;
            
        } catch (Exception e) {
            logger.error("发送HTTP请求失败", e);
            throw new RuntimeException("发送HTTP请求失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析响应数据
     * 对应原 BusUtil.send 方法中的响应解析逻辑
     */
    private Map<String, Object> parseResponse(String response, String empname, String orgcode) {
        try {
            if (BusinessUtil.isEmpty(response)) {
                logger.warn("发起人:{}||机构：{}||{}", empname, orgcode, createErrorResponse("请求失败"));
                return createErrorResponse("请求失败");
            }
            
            // 解析JSON响应
            JSONObject jsonResponse = JSONObject.parseObject(response);
            
            if (jsonResponse == null || BusinessUtil.isEmpty(jsonResponse.toJSONString())) {
                logger.warn("发起人:{}||机构：{}||{}", empname, orgcode, createErrorResponse("请求失败"));
                return createErrorResponse("请求失败");
            }
            
            // 转换为Map
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<String, Object> entry : jsonResponse.entrySet()) {
                result.put(entry.getKey(), entry.getValue());
            }
            
            logger.debug("解析的响应数据: {}", result);
            return result;
            
        } catch (Exception e) {
            logger.error("解析响应数据失败: {}", response, e);
            return createErrorResponse("解析响应数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建错误响应
     * 对应原 BusUtil.ErrJson 方法
     */
    private Map<String, Object> createErrorResponse(String errorMessage) {
        return createErrorResponseWithCode("1", errorMessage);
    }
    
    /**
     * 创建指定错误码的错误响应
     * 对应原 BusUtil.ErrJson 方法
     */
    private Map<String, Object> createErrorResponseWithCode(String code, String errorMessage) {
        Map<String, Object> errorResponse = new HashMap<>();
        
        // 组装错误head
        Map<String, Object> head = new HashMap<>();
        head.put("rescode", code);
        head.put("resmsg", errorMessage);
        
        errorResponse.put("head", head);
        
        return errorResponse;
    }
    
    /**
     * 获取嵌套对象的值
     * 辅助方法，用于获取 head.sendseqno 等嵌套值
     */
    private String getNestedValue(Map<String, Object> map, String path) {
        try {
            String[] parts = path.split("\\.");
            Object current = map;
            
            for (String part : parts) {
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(part);
                } else {
                    return "";
                }
            }
            
            return current != null ? current.toString() : "";
        } catch (Exception e) {
            return "";
        }
    }
    
    /**
     * 生成发送序列号
     * 对应原 BusUtil 中的 sendseqno 生成逻辑
     */
    private String generateSendSeqNo() {
        return "SEQ" + BusinessUtil.getCurrentTimestamp();
    }
    
    /**
     * 检查报文中的必填字段
     * 对应原 BusUtil.send 方法中的字段检查逻辑
     */
    private boolean validateRequestMessage(Map<String, Object> requestMessage) {
        String code = (String) requestMessage.get("code");
        return !"9998".equals(code);
    }
    
    /**
     * 健康检查
     */
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "BusinessServiceImpl");
        result.put("timestamp", BusinessUtil.getCurrentDateTime());
        result.put("targetUrl", targetUrl);
        return result;
    }
}