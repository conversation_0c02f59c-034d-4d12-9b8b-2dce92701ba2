package com.psbc.cpufp.socket;

import com.psbc.cpufp.config.XmysfzjjgProperties;
import com.psbc.cpufp.util.PortChecker;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PayServerSocket启动测试
 */
@SpringBootTest
@ActiveProfiles("test")
class PayServerSocketTest {

    @Autowired
    private PayServerSocket payServerSocket;

    @Autowired
    private XmysfzjjgProperties properties;

    @Test
    void testPayServerSocketBeanCreation() {
        // 验证PayServerSocket Bean是否正确创建
        assertNotNull(payServerSocket, "PayServerSocket应该被正确注入");
    }

    @Test
    void testPropertiesConfiguration() {
        // 验证配置是否正确加载
        assertNotNull(properties, "XmysfzjjgProperties应该被正确注入");
        assertNotNull(properties.getWgPort(), "WG_PORT应该被正确配置");
        
        Integer port = properties.getWgPort();
        assertTrue(port > 0 && port < 65536, "端口号应该在有效范围内");
        
        System.out.println("配置的Netty服务器端口: " + port);
    }

    @Test
    void testNettyServerStartup() {
        // 等待一段时间让Netty服务器启动
        try {
            Thread.sleep(5000); // 等待5秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 检查Netty服务器是否启动
        Integer port = properties.getWgPort();
        if (port != null) {
            boolean isServerRunning = PortChecker.waitForPort("localhost", port, 10000);
            assertTrue(isServerRunning, "Netty服务器应该在端口 " + port + " 上启动");
        }
    }
}
