# SSL/TLS协议不一致问题排查指南

## 概述
本指南帮助您诊断和解决SSL握手失败、协议不一致等常见问题。

## 常见错误类型

### 1. SSLHandshakeException: Received fatal alert: handshake_failure
**原因分析：**
- 客户端和服务器支持的TLS协议版本不匹配
- 加密套件不兼容
- 证书验证失败

**解决方案：**
```java
// 设置兼容的TLS协议版本
tlsParams.setSecureSocketProtocol("TLSv1.2");

// 禁用证书验证（仅开发环境）
tlsParams.setDisableCNCheck(true);
tlsParams.setTrustManagers(trustAllCerts);
```

### 2. SSLHandshakeException: protocol_version
**原因分析：**
- 客户端使用的协议版本过旧
- 服务器不支持客户端请求的协议版本

**解决方案：**
```java
// 升级到TLS 1.2或更高版本
System.setProperty("https.protocols", "TLSv1.2,TLSv1.3");
```

### 3. SSLHandshakeException: certificate_unknown
**原因分析：**
- 服务器证书不被信任
- 证书链不完整
- 证书已过期

**解决方案：**
```java
// 自定义TrustManager处理证书验证
TrustManager[] trustManagers = new TrustManager[] {
    new X509TrustManager() {
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
            // 自定义证书验证逻辑
        }
        // ...
    }
};
```

## 测试工具使用

### 1. 运行SSL测试
```bash
# 运行测试脚本
run_ssl_tests.bat

# 或者直接运行特定测试
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.SSLDiagnosticTool" -Dexec.classpathScope=test
```

### 2. 启用SSL调试
```bash
# 启动应用时添加SSL调试参数
java -Djavax.net.debug=ssl,handshake -jar your-app.jar
```

### 3. 检查服务器SSL配置
```bash
# 使用openssl检查服务器SSL配置
openssl s_client -connect ***********:8443 -servername ***********

# 检查支持的协议版本
openssl s_client -connect ***********:8443 -tls1_2
openssl s_client -connect ***********:8443 -tls1_3
```

## 协议不一致场景模拟

### 场景1: 客户端协议版本过低
```java
// 模拟客户端只支持TLS 1.0
sslSocket.setEnabledProtocols(new String[]{"TLSv1"});
// 预期结果: 握手失败
```

### 场景2: 加密套件不匹配
```java
// 设置不兼容的加密套件
sslSocket.setEnabledCipherSuites(new String[]{"TLS_RSA_WITH_NULL_MD5"});
// 预期结果: 握手失败
```

### 场景3: 证书验证失败
```java
// 启用严格的证书验证
SSLContext.getInstance("TLS").init(null, null, null);
// 预期结果: 证书验证失败
```

## 配置建议

### 开发环境配置
```yaml
# application-dev.yml
webservice:
  client:
    url: https://***********:8443/xmysfzjjg/wservices/IWebServiceService?wsdl
    ssl:
      trust-all: true
      protocol: TLSv1.2
      verify-hostname: false
```

### 生产环境配置
```yaml
# application-prod.yml
webservice:
  client:
    url: https://prod-server:8443/xmysfzjjg/wservices/IWebServiceService?wsdl
    ssl:
      trust-all: false
      protocol: TLSv1.2
      verify-hostname: true
      keystore: classpath:client.jks
      keystore-password: ${SSL_KEYSTORE_PASSWORD}
```

## 系统属性配置

### JVM启动参数
```bash
# 设置支持的TLS协议
-Dhttps.protocols=TLSv1.2,TLSv1.3
-Djdk.tls.client.protocols=TLSv1.2,TLSv1.3

# 启用SSL调试
-Djavax.net.debug=ssl,handshake

# 禁用证书验证（仅开发环境）
-Dcom.sun.net.ssl.checkRevocation=false
-Dtrust_all_cert=true
```

### 代码中设置系统属性
```java
// 在应用启动时设置
System.setProperty("https.protocols", "TLSv1.2,TLSv1.3");
System.setProperty("jdk.tls.client.protocols", "TLSv1.2,TLSv1.3");
System.setProperty("com.sun.net.ssl.checkRevocation", "false");
```

## 常见问题解决

### Q1: 为什么TLS 1.3连接失败？
**A:** 可能原因：
- 服务器不支持TLS 1.3
- JVM版本过低（需要Java 11+）
- 加密套件不兼容

**解决方案：** 降级到TLS 1.2

### Q2: 证书验证总是失败怎么办？
**A:** 检查：
- 证书是否过期
- 证书链是否完整
- 主机名是否匹配
- 是否为自签名证书

**解决方案：** 
- 更新证书
- 配置自定义TrustManager
- 禁用主机名验证（仅开发环境）

### Q3: 如何确定服务器支持的协议版本？
**A:** 使用诊断工具：
```bash
# 运行SSL诊断工具
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.SSLDiagnosticTool" -Dexec.classpathScope=test
```

## 监控和日志

### 启用详细日志
```xml
<!-- logback-spring.xml -->
<logger name="com.psbc.cpufp.ssl" level="DEBUG"/>
<logger name="org.apache.cxf.transport.http" level="DEBUG"/>
<logger name="javax.net.ssl" level="DEBUG"/>
```

### 关键日志信息
- SSL握手开始/完成时间
- 使用的协议版本
- 选择的加密套件
- 证书验证结果

## 最佳实践

1. **协议版本选择**
   - 优先使用TLS 1.2
   - 避免使用SSL 3.0及以下版本
   - 根据服务器支持情况选择TLS 1.3

2. **证书管理**
   - 定期更新证书
   - 使用完整的证书链
   - 生产环境避免自签名证书

3. **安全配置**
   - 生产环境启用证书验证
   - 使用强加密套件
   - 定期更新SSL/TLS配置

4. **错误处理**
   - 实现重试机制
   - 记录详细错误信息
   - 提供降级方案
