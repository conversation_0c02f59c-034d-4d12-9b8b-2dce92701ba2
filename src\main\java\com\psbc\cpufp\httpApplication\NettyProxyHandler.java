package com.psbc.cpufp.httpApplication;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.psbc.cpufp.dto.NettyRequestVO;
import com.psbc.cpufp.dto.NettyResponseVO;
import com.psbc.cpufp.service.NettyClientService;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

/**
 * Netty代理HTTP处理器
 * 处理HTTP请求并转发到8888端口的Netty服务
 */
@Slf4j
public class NettyProxyHandler implements HttpHandler {
    
    private final NettyClientService nettyClientService;
    private final ObjectMapper objectMapper;
    
    public NettyProxyHandler(ApplicationContext applicationContext) {
        this.nettyClientService = applicationContext.getBean(NettyClientService.class);
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String requestPath = exchange.getRequestURI().getPath();
        String requestMethod = exchange.getRequestMethod();
        
        log.info("收到Netty代理HTTP请求: {} {}", requestMethod, requestPath);
        
        NettyResponseVO response;
        
        try {
            if (!"POST".equalsIgnoreCase(requestMethod)) {
                response = NettyResponseVO.error("-1", "不支持的请求方法: " + requestMethod);
                sendJsonResponse(exchange, response, 405);
                return;
            }
            String xmlMessage = readRequestBody(exchange);
            // 发送到Netty服务器
            String nettyResponse = nettyClientService.sendMessage(xmlMessage);
            
            // 构造成功响应
            response = NettyResponseVO.success(nettyResponse);
            
            log.info("Netty代理响应: {}", response);
            
            // 发送响应
            sendJsonResponse(exchange, response, 200);
            
        } catch (Exception e) {
            log.error("处理Netty代理请求异常: {}", e.getMessage(), e);
            response = NettyResponseVO.error("-1", "请求处理异常: " + e.getMessage());
            sendJsonResponse(exchange, response, 500);
        }
    }
    
    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            StringBuilder requestBody = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
            return requestBody.toString();
        }
    }
    
    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(HttpExchange exchange, NettyResponseVO response, int statusCode) throws IOException {
        try {
            String jsonResponse = objectMapper.writeValueAsString(response);
            
            exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
            byte[] responseBytes = jsonResponse.getBytes(StandardCharsets.UTF_8);
            exchange.sendResponseHeaders(statusCode, responseBytes.length);
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(responseBytes);
            }
            
            log.info("发送响应: {}", jsonResponse);
        } catch (Exception e) {
            log.error("发送响应失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}