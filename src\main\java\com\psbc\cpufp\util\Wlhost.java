package com.psbc.cpufp.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class Wlhost {

    private static final int LEN_SIZE = 6;

    /**
     * 发送消息到外联系统
     * 协议格式：6位长度字段 + 消息体
     *
     * @param ip 外联系统IP
     * @param port 外联系统端口
     * @param msg 要发送的消息
     * @return 外联系统响应消息
     */
    public String sendToWl(String ip, int port, String msg) {
        log.info("连接外联系统: {}:{}", ip, port);

        Socket socket = null;
        InputStream is = null;
        OutputStream os = null;
        String rspmsg = null;

        try {
            // 建立连接
            socket = new Socket(ip, port);
            is = socket.getInputStream();
            os = socket.getOutputStream();

            // 构造请求消息：6位长度前缀 + 消息体
            byte[] msgBytes = msg.getBytes(StandardCharsets.UTF_8);
            String reqmsg = String.format("%06d", msgBytes.length) + msg;
            log.info("向外联发送请求报文: {}", reqmsg);

            // 发送请求
            os.write(reqmsg.getBytes(StandardCharsets.UTF_8));
            os.flush();

            // 读取响应头（6字节长度字段）
            byte[] retHead = new byte[LEN_SIZE];
            log.info("读取外联系统返回报文头...");
            int n = is.read(retHead);

            if (n < LEN_SIZE) {
                String headStr = new String(retHead, 0, Math.max(0, n), StandardCharsets.UTF_8);
                log.error("接收外联系统错误: 报文头长度不足, 期望{}字节, 实际{}字节, 内容: [{}]",
                         LEN_SIZE, n, headStr);
                throw new Exception("接收外联系统报文错误: 报文头长度不足");
            }

            String headStr = new String(retHead, StandardCharsets.UTF_8);
            log.info("外联系统返回报文头: [{}]", headStr);

            // 解析消息体长度
            int len;
            try {
                len = Integer.parseInt(headStr.trim());
                log.info("解析到消息体长度: {} 字节", len);
            } catch (NumberFormatException e) {
                log.error("无法解析报文头长度字段: [{}]", headStr);
                throw new Exception("接收外联系统报文错误: 报文头格式错误");
            }

            if (len <= 0 || len > 10 * 1024 * 1024) { // 限制最大10MB
                log.error("消息体长度异常: {} 字节", len);
                throw new Exception("接收外联系统报文错误: 消息体长度异常");
            }

            // 读取消息体
            byte[] msgbody = new byte[len];
            int totalRead = 0;

            while (totalRead < len) {
                int currentRead = is.read(msgbody, totalRead, len - totalRead);
                if (currentRead == -1) {
                    log.error("连接在读取消息体时关闭, 已读取: {}/{} 字节", totalRead, len);
                    throw new Exception("接收外联系统报文错误: 连接意外关闭");
                }
                totalRead += currentRead;
            }

            if (totalRead < len) {
                log.error("读取消息体不完整, 期望{}字节, 实际{}字节", len, totalRead);
                throw new Exception("接收外联系统报文错误: 读取长度小于报文头标识长度");
            }

            rspmsg = new String(msgbody, StandardCharsets.UTF_8);
            log.info("成功接收外联系统响应, 长度: {} 字节", rspmsg.length());

        } catch (UnknownHostException e) {
            log.error("外联系统主机不可达: {}:{}", ip, port, e);
            rspmsg = "外联系统连接失败: 主机不可达";
        } catch (IOException e) {
            log.error("外联系统IO异常: {}:{}", ip, port, e);
            rspmsg = "外联系统连接失败: " + e.getMessage();
        } catch (Exception e) {
            log.error("外联系统通信异常: {}:{}", ip, port, e);
            rspmsg = "外联系统通信异常: " + e.getMessage();
        } finally {
            // 关闭资源
            closeQuietly(is, "InputStream");
            closeQuietly(os, "OutputStream");
            closeQuietly(socket, "Socket");
        }

        return rspmsg;
    }

    /**
     * 安全关闭资源
     */
    private void closeQuietly(Closeable resource, String resourceName) {
        if (resource != null) {
            try {
                resource.close();
                log.debug("{} 已关闭", resourceName);
            } catch (IOException e) {
                log.warn("关闭{}时发生错误: {}", resourceName, e.getMessage());
            }
        }
    }
}
