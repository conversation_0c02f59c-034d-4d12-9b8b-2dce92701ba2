package com.psbc.cpufp.httpApplication;

import cn.hutool.http.HttpRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.psbc.cpufp.service.BusinessServiceImpl;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 业务通用HTTP处理器
 * 处理所有/api/business路径下的请求，转发到12358端口的业务服务
 */
@Slf4j
public class BusinessUniversalHandler implements HttpHandler {
    
    private final String targetBaseUrl;
    private final ObjectMapper objectMapper;
    private final ApplicationContext applicationContext;
    
    public BusinessUniversalHandler(String targetBaseUrl, ApplicationContext applicationContext) {
        this.targetBaseUrl = targetBaseUrl;
        this.objectMapper = new ObjectMapper();
        this.applicationContext = applicationContext;
    }
    
    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String requestPath = exchange.getRequestURI().getPath();
        String requestMethod = exchange.getRequestMethod();
        
        log.info("收到业务HTTP请求: {} {}", requestMethod, requestPath);
        
        try {
            // 将8446端口的路径转换为12358端口的路径
            // 例如: /api/business/list -> /api/business/list
            String targetPath = convertToTargetPath(requestPath);
            String targetUrl = targetBaseUrl + targetPath;
            
            log.info("业务转发路径映射: {} -> {}", requestPath, targetPath);
            log.info("目标URL: {}", targetUrl);
            
            String responseBody;
            
            if ("POST".equalsIgnoreCase(requestMethod)) {
                // 读取请求体
                String requestBody = readRequestBody(exchange);
                log.info("请求体: {}", requestBody);
                
                // 检查是否为业务接口，如果是则调用本地BusinessServiceImpl
                if (requestPath.startsWith("/api/xzp/spf/20002")) {
                    responseBody = handleBusinessRequest(requestPath, requestBody);
                } else {
                    // 其他请求直接转发
                    responseBody = HttpRequest.post(targetUrl)
                            .header("Content-Type", "application/json")
                            .body(requestBody)
                            .timeout(30000) // 30秒超时
                            .execute()
                            .body();
                }
            } else if ("GET".equalsIgnoreCase(requestMethod)) {
                // 检查是否为业务健康检查接口
                if ("/api/business/health".equals(requestPath)) {
                    responseBody = handleBusinessRequest(requestPath, "");
                } else {
                    // 转发GET请求
                    responseBody = HttpRequest.get(targetUrl)
                            .timeout(30000) // 30秒超时
                            .execute()
                            .body();
                }
            } else {
                responseBody = "{\"success\":false,\"message\":\"不支持的请求方法: " + requestMethod + "\"}";
                exchange.sendResponseHeaders(405, responseBody.getBytes(StandardCharsets.UTF_8).length);
                writeResponse(exchange, responseBody);
                return;
            }
            
            log.info("业务转发响应: {}", responseBody);
            
            // 发送响应
            exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
            exchange.sendResponseHeaders(200, responseBody.getBytes(StandardCharsets.UTF_8).length);
            writeResponse(exchange, responseBody);
            
        } catch (IOException e) {
            log.error("处理业务请求IO异常: {}", e.getMessage(), e);
            String errorResponse = "{\"success\":false,\"message\":\"请求处理异常: " + e.getMessage().replace("\"", "\\\"")+ "\"}";
            exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
            exchange.sendResponseHeaders(500, errorResponse.getBytes(StandardCharsets.UTF_8).length);
            writeResponse(exchange, errorResponse);
        } catch (RuntimeException e) {
            log.error("处理业务请求运行时异常: {}", e.getMessage(), e);
            String errorResponse = "{\"success\":false,\"message\":\"请求处理异常: " + e.getMessage().replace("\"", "\\\"")+ "\"}";
            exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
            exchange.sendResponseHeaders(500, errorResponse.getBytes(StandardCharsets.UTF_8).length);
            writeResponse(exchange, errorResponse);
        }
    }
    
    /**
     * 处理业务请求，调用本地BusinessServiceImpl
     */
    private String handleBusinessRequest(String requestPath, String requestBody) {
        try {
            BusinessServiceImpl businessService = applicationContext.getBean(BusinessServiceImpl.class);
            Map<String, Object> result;
            
            // 解析请求体为Map
            Map<String, Object> criteria = null;
            if (!requestBody.isEmpty()) {
                try {
                    JsonNode jsonNode = objectMapper.readTree(requestBody);
                    criteria = objectMapper.convertValue(jsonNode, Map.class);
                } catch (Exception e) {
                    log.error("解析请求体失败: {}", e.getMessage());
                    return "{\"success\":false,\"message\":\"请求体格式错误\"}";
                }
            }
            
            // 根据请求路径调用相应的业务方法
            switch (requestPath) {
                case "/api/xzp/spf/20002":
                    result = businessService.list(criteria);
                    break;
                case "/api/business/list1":
                    result = businessService.list1(criteria);
                    break;
                case "/api/business/ordin":
                    result = businessService.ordin(criteria);
                    break;
                case "/api/business/ordin1":
                    result = businessService.ordin1(criteria);
                    break;
                case "/api/business/ordin2":
                    result = businessService.ordin2(criteria);
                    break;
                case "/api/business/health":
                    result = businessService.health();
                    break;
                default:
                    return "{\"success\":false,\"message\":\"不支持的业务接口: " + requestPath + "\"}";
            }
            
            // 将结果转换为JSON字符串
            return objectMapper.writeValueAsString(result);
            
        } catch (Exception e) {
            log.error("处理业务请求失败: {}", e.getMessage(), e);
            return "{\"success\":false,\"message\":\"业务处理异常: " + e.getMessage().replace("\"", "\\\"") + "\"}";
        }
    }
    
    /**
     * 将8446端口的路径转换为12358端口的路径
     * 保持路径不变，直接转发
     * 
     * @param requestPath 8446端口接收到的请求路径
     * @return 12358端口对应的路径
     */
    private String convertToTargetPath(String requestPath) {
        // 直接返回原路径，保持API路径一致
//        return requestPath;
        return "/api/admin/xzp/queryYxjfLsxdye";
    }
    
    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            StringBuilder requestBody = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
            return requestBody.toString();
        }
    }
    
    /**
     * 写入响应
     */
    private void writeResponse(HttpExchange exchange, String response) throws IOException {
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(response.getBytes(StandardCharsets.UTF_8));
        }
    }
}