package com.psbc.cpufp.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Wlhost类测试
 * 验证6位长度前缀协议的正确实现
 */
@Slf4j
class WlhostTest {

    private Wlhost wlhost;
    private MockExternalServer mockServer;

    @BeforeEach
    void setUp() {
        wlhost = new Wlhost();
        mockServer = new MockExternalServer();
    }

    @Test
    @DisplayName("测试正常的消息发送和接收")
    void testNormalMessageExchange() throws Exception {
        // 启动模拟服务器
        int port = mockServer.start();
        
        try {
            // 准备测试消息
            String testMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<content><head><serviceno>TEST001</serviceno></head>" +
                    "<body><data>test message</data></body></content>";
            
            // 设置模拟服务器的响应
            String expectedResponse = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                    "<response><status>success</status><message>处理成功</message></response>";
            mockServer.setResponse(expectedResponse);
            
            // 发送消息
            String actualResponse = wlhost.sendToWl("localhost", port, testMessage);
            
            // 验证结果
            assertNotNull(actualResponse, "响应不应该为null");
            assertEquals(expectedResponse, actualResponse, "响应内容应该匹配");
            
            // 验证服务器收到的消息格式
            String receivedMessage = mockServer.getReceivedMessage();
            assertNotNull(receivedMessage, "服务器应该收到消息");
            
            // 验证消息格式：应该是 "长度前缀 + 消息体"
            assertTrue(receivedMessage.length() >= 6, "消息应该包含6位长度前缀");
            
            String lengthPrefix = receivedMessage.substring(0, 6);
            String messageBody = receivedMessage.substring(6);
            
            // 验证长度前缀格式
            assertTrue(lengthPrefix.matches("\\d{6}"), "长度前缀应该是6位数字");
            
            // 验证长度前缀的值
            int expectedLength = testMessage.getBytes(StandardCharsets.UTF_8).length;
            int actualLength = Integer.parseInt(lengthPrefix);
            assertEquals(expectedLength, actualLength, "长度前缀应该等于消息体的字节长度");
            
            // 验证消息体
            assertEquals(testMessage, messageBody, "消息体应该匹配原始消息");
            
        } finally {
            mockServer.stop();
        }
    }

    @Test
    @DisplayName("测试连接失败的情况")
    void testConnectionFailure() {
        // 使用一个不存在的端口
        String response = wlhost.sendToWl("localhost", 99999, "test message");
        
        assertNotNull(response, "即使连接失败也应该返回错误信息");
        assertTrue(response.contains("连接失败") || response.contains("Connection refused"), 
                  "应该包含连接失败的错误信息");
    }

    @Test
    @DisplayName("测试空消息的处理")
    void testEmptyMessage() throws Exception {
        int port = mockServer.start();
        
        try {
            mockServer.setResponse("OK");
            
            String response = wlhost.sendToWl("localhost", port, "");
            
            assertNotNull(response, "空消息也应该有响应");
            assertEquals("OK", response, "应该收到服务器的响应");
            
            // 验证空消息的长度前缀
            String receivedMessage = mockServer.getReceivedMessage();
            assertEquals("000000", receivedMessage, "空消息应该有长度前缀000000");
            
        } finally {
            mockServer.stop();
        }
    }

    @Test
    @DisplayName("测试中文消息的处理")
    void testChineseMessage() throws Exception {
        int port = mockServer.start();
        
        try {
            String chineseMessage = "测试中文消息处理能力";
            String expectedResponse = "中文响应消息";
            
            mockServer.setResponse(expectedResponse);
            
            String response = wlhost.sendToWl("localhost", port, chineseMessage);
            
            assertEquals(expectedResponse, response, "中文消息应该正确处理");
            
            // 验证中文消息的长度计算（应该按UTF-8字节长度计算）
            String receivedMessage = mockServer.getReceivedMessage();
            String lengthPrefix = receivedMessage.substring(0, 6);
            String messageBody = receivedMessage.substring(6);
            
            int expectedLength = chineseMessage.getBytes(StandardCharsets.UTF_8).length;
            int actualLength = Integer.parseInt(lengthPrefix);
            
            assertEquals(expectedLength, actualLength, "中文消息的长度应该按UTF-8字节计算");
            assertEquals(chineseMessage, messageBody, "中文消息体应该正确传输");
            
        } finally {
            mockServer.stop();
        }
    }

    /**
     * 模拟外联系统服务器
     * 实现6位长度前缀协议
     */
    private static class MockExternalServer {
        private ServerSocket serverSocket;
        private String responseMessage = "DEFAULT_RESPONSE";
        private String receivedMessage;
        private CompletableFuture<Void> serverFuture;

        public int start() throws IOException {
            serverSocket = new ServerSocket(0); // 使用随机端口
            int port = serverSocket.getLocalPort();
            
            serverFuture = CompletableFuture.runAsync(() -> {
                try {
                    log.info("模拟外联服务器启动，端口: {}", port);
                    
                    while (!serverSocket.isClosed()) {
                        try (Socket clientSocket = serverSocket.accept()) {
                            handleClient(clientSocket);
                        } catch (IOException e) {
                            if (!serverSocket.isClosed()) {
                                log.error("处理客户端连接时发生错误", e);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("模拟服务器运行异常", e);
                }
            });
            
            return port;
        }

        private void handleClient(Socket clientSocket) throws IOException {
            InputStream in = clientSocket.getInputStream();
            OutputStream out = clientSocket.getOutputStream();
            
            // 按照6位长度前缀协议读取消息
            byte[] lengthBytes = new byte[6];
            int n = in.read(lengthBytes);
            if (n != 6) {
                log.error("读取长度前缀失败，期望6字节，实际{}字节", n);
                return;
            }
            
            String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8);
            int messageLength = Integer.parseInt(lengthStr.trim());
            
            byte[] messageBytes = new byte[messageLength];
            int totalRead = 0;
            while (totalRead < messageLength) {
                int currentRead = in.read(messageBytes, totalRead, messageLength - totalRead);
                if (currentRead == -1) break;
                totalRead += currentRead;
            }
            
            // 保存收到的完整消息（包含长度前缀）
            receivedMessage = lengthStr + new String(messageBytes, StandardCharsets.UTF_8);
            
            log.info("模拟服务器收到消息: {}", receivedMessage);
            
            // 按照协议发送响应
            byte[] responseBytes = responseMessage.getBytes(StandardCharsets.UTF_8);
            String responseLengthStr = String.format("%06d", responseBytes.length);
            
            out.write(responseLengthStr.getBytes(StandardCharsets.UTF_8));
            out.write(responseBytes);
            out.flush();
            
            log.info("模拟服务器发送响应: {}{}", responseLengthStr, responseMessage);
        }

        public void setResponse(String response) {
            this.responseMessage = response;
        }

        public String getReceivedMessage() {
            return receivedMessage;
        }

        public void stop() {
            try {
                if (serverSocket != null && !serverSocket.isClosed()) {
                    serverSocket.close();
                }
                if (serverFuture != null) {
                    serverFuture.get(1, TimeUnit.SECONDS);
                }
            } catch (Exception e) {
                log.warn("停止模拟服务器时发生错误", e);
            }
        }
    }
}
