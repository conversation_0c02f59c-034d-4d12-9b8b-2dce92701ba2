### 动态目标URL转发示例

### 1. 使用自定义targetUrl的请求
POST http://127.0.0.1:8446/api/business/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "queryParams": {
    "status": "active",
    "type": "business"
  },
  "targetUrl": "http://192.168.1.100:9702/api/custom/endpoint"
}

### 2. 使用默认配置的请求（向后兼容）
POST http://127.0.0.1:8446/api/business/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "queryParams": {
    "status": "active"
  }
}

### 3. 转发到不同服务器的示例
POST http://127.0.0.1:8446/api/business/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 20,
  "filters": {
    "date": "2024-01-01",
    "department": "IT"
  },
  "targetUrl": "http://test-server:8080/api/v2/data/query"
}

### 4. 转发到HTTPS服务的示例
POST http://127.0.0.1:8446/api/business/list
Content-Type: application/json

{
  "searchCriteria": {
    "keyword": "test",
    "category": "finance"
  },
  "targetUrl": "https://secure-api.example.com:443/api/secure/endpoint"
}

### 5. 其他接口不受影响（targetUrl会被忽略）
POST http://127.0.0.1:8446/api/business/ordin
Content-Type: application/json

{
  "action": "query",
  "params": {
    "id": "12345"
  },
  "targetUrl": "http://this-will-be-ignored:8080/api/endpoint"
}