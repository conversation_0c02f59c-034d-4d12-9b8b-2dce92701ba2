package com.psbc.cpufp.httpApplication;

import cn.hutool.http.HttpRequest;
import com.psbc.cpufp.config.ZhbgProperties;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.net.ssl.SSLException;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/** 处理/myserver路径请求的处理器类，http请求 */
@Slf4j
public class MyHttpHandler implements HttpHandler {

	@Resource
	private ZhbgProperties zhbgProperties;

	@Override
	public void handle(HttpExchange httpExchange) {
		log.info("进入请求综合办公获取按揭贷款信息接口");
		try {
			String reqMsg = getRequestParam(httpExchange);
			log.info("请求综合办公参数：{}", reqMsg);

			String url = zhbgProperties.getZhbgPostUrl() + zhbgProperties.getUrl30001();
			String resp = send(url, reqMsg);
			handleResponse(httpExchange, resp);
		} catch (Exception ex) {
			log.error("处理请求异常", ex);
		}
	}

	/** 发送HTTP POST请求到综合办公系统 */
	public static String send(String url, String jsonStr) {
		log.info("转发到：{}", url);
		log.info("传入参数：{}", jsonStr);

		try {
			return HttpRequest.post(url)
					.header("Content-Type", "application/json")
					.body(jsonStr)
					.execute()
					.body();
		} catch (Exception e) {
			log.error("请求响应异常", e);
			return e.getMessage();
		}
	}

	/** 发送请求到综合办公系统并获取响应 */
	private String sendZhbg(String reqmsg, String serviceno) {
		if (!"30001".equals(serviceno) && !"20006".equals(serviceno)) {
			return "未定义的接口信息";
		}

		String baseUrl = zhbgProperties.getZhbgPostUrl();
		String zhbgurl = baseUrl + ("30001".equals(serviceno) ?
				zhbgProperties.getUrl30001() : zhbgProperties.getUrl20006());

		log.info("开始请求{}接口, URL: {}", serviceno, zhbgurl);

		HttpURLConnection conn = null;
		try {
			URL url = zhbgurl.startsWith("https:") ?
					new URL(null, zhbgurl, new sun.net.www.protocol.https.Handler()) :
					new URL(zhbgurl);

			conn = (HttpURLConnection) url.openConnection();
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-type",
					"30001".equals(serviceno) ? "application/json" : "application/xml");
			conn.setConnectTimeout(30000);
			conn.setReadTimeout(30000);
			conn.connect();

			// 发送请求数据
			try (OutputStreamWriter out = new OutputStreamWriter(
					conn.getOutputStream(), StandardCharsets.UTF_8)) {
				out.write(reqmsg);
				out.flush();
			}

			// 读取响应数据
			try (BufferedReader reader = new BufferedReader(
					new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
				String response = reader.lines()
						.collect(StringBuilder::new, StringBuilder::append, StringBuilder::append)
						.toString();
				log.info("接口返回：{}", response);
				return response;
			}
		} catch (MalformedURLException e) {
			String errorMsg = String.format("请求%s接口URL格式错误：%s", serviceno, zhbgurl);
			log.error(errorMsg, e);
			return errorMsg;
		} catch (ProtocolException e) {
			String errorMsg = String.format("请求%s接口协议错误：%s", serviceno, e.getMessage());
			log.error(errorMsg, e);
			return errorMsg;
		} catch (SSLException e) {
			String errorMsg = String.format("请求%s接口SSL/TLS错误：%s", serviceno, e.getMessage());
			log.error(errorMsg, e);
			return errorMsg;
		} catch (SocketTimeoutException e) {
			String errorMsg = String.format("请求%s接口超时（连接/读取超时）", serviceno);
			log.error(errorMsg, e);
			return errorMsg;
		} catch (IOException e) {
			String errorMsg = String.format("请求%s接口IO异常：%s", serviceno, e.getMessage());
			log.error(errorMsg, e);
			// 可添加重试逻辑（需根据业务场景判断）
			return errorMsg;
		} catch (Exception e) {
			// 捕获其他未预期的异常（保持通用性）
			String errorMsg = String.format("请求%s接口未知异常：%s", serviceno, e.getMessage());
			log.error(errorMsg, e);
			return errorMsg;
		}  finally {
			if (conn != null) {
				conn.disconnect();
			}
			log.info("请求{}接口结束", serviceno);
		}
	}

	/** 获取请求头信息 */
	private Map<String, String> getRequestHeader(HttpExchange httpExchange) {
		Map<String, String> headerMap = new HashMap<>();
		httpExchange.getRequestHeaders().forEach((key, value) ->
				headerMap.put(key, String.join(",", value)));
		return headerMap;
	}

	/** 获取请求参数 */
	private String getRequestParam(HttpExchange httpExchange) throws IOException {
		if ("GET".equals(httpExchange.getRequestMethod())) {
			return httpExchange.getRequestURI().getQuery();
		}

		try (BufferedReader reader = new BufferedReader(
				new InputStreamReader(httpExchange.getRequestBody(), StandardCharsets.UTF_8))) {
			return reader.lines()
					.collect(StringBuilder::new, StringBuilder::append, StringBuilder::append)
					.toString();
		}
	}

	/** 处理HTTP响应 */
	private void handleResponse(HttpExchange httpExchange, String responseText) throws IOException {
		byte[] responseBytes = responseText.getBytes(StandardCharsets.UTF_8);

		httpExchange.getResponseHeaders().add("Content-Type", "text/html;charset=utf-8");
		httpExchange.sendResponseHeaders(200, responseBytes.length);

		try (OutputStream out = httpExchange.getResponseBody()) {
			out.write(responseBytes);
			out.flush();
		}
	}
}