package com.psbc.cpufp.controller;

import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/xzp")
public class XzpController {
    @Value("${other.zhbg_ajdkUrl}")
    private String ajdkUrl;

    @RequestMapping(value = "/queryYxjfLsxdye", method = RequestMethod.POST)
    public String queryYxjfLsxdye(@RequestBody String jsonObject) {
        log.info("【queryYxjfLsxdye】请求参数json：{}", jsonObject);

        if (ajdkUrl == null || ajdkUrl.isEmpty()) {
            return "请求综合办公地址错误，请联系管理员确认配置是否正常！";
        }

        return send(ajdkUrl, jsonObject);
    }

    public static String send(String url, String jsonStr) {
        log.info("开始转发请求：{}", url);

        try {
            return HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(jsonStr)
                    .execute()
                    .body();
        } catch (Exception e) {
            log.error("请求综合办公接口异常", e);
            return e.getMessage().contains("Connection refused: connect")
                    ? "请求综合办公接口连接超时，请检查网络！"
                    : "请求综合办公接口响应异常：" + e.getMessage();
        } finally {
            log.info("请求综合办公获取按揭贷款信息进程执行完成！");
        }
    }

    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public String test(@RequestBody String jsonObject) {
        log.info("【test】POST请求参数json：{}", jsonObject);
        return "【test】请求参数json：" + jsonObject;
    }

    @RequestMapping(value = "/testGet", method = RequestMethod.GET)
    public String testGet() {
        log.info("testGet测试方法");
        return "testGet测试方法请求返回";
    }

    private static String sendZhbg(String reqmsg, String serviceno) {
        if (!"30001".equals(serviceno)) {
            return "未定义的接口信息，无法查询！";
        }

        String baseUrl = "http://127.0.0.1:9091/";
        String zhbgurl = baseUrl + "api/admin/xzp/queryYxjfLsxdye";
        log.info("开始请求30001接口,传入json参数：{}", reqmsg);
        log.info("请求URL：{}", zhbgurl);

        HttpURLConnection conn = null;
        try {
            URL url = zhbgurl.startsWith("https:")
                    ? new URL(null, zhbgurl, new sun.net.www.protocol.https.Handler())
                    : new URL(zhbgurl);

            conn = (HttpURLConnection) url.openConnection();
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-type", "application/json");
            conn.setConnectTimeout(30000);
            conn.setReadTimeout(30000);
            conn.connect();

            // 写入请求数据
            try (OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8)) {
                out.write(reqmsg);
                out.flush();
            } catch (IOException e) {
                log.error("写入请求数据失败", e);
                return "请求发送失败：" + e.getMessage();
            }

            // 读取响应数据
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                String result = response.toString();
                log.info("接口返回：{}", result);
                return result;
            } catch (IOException e) {
                log.error("读取响应数据失败", e);
                return "读取响应失败：" + e.getMessage();
            }
        } catch (MalformedURLException e) {
            log.error("URL格式错误", e);
            return "URL格式错误：" + e.getMessage();
        } catch (IOException e) {
            log.error("网络连接异常", e);
            return "网络连接异常：" + e.getMessage();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
            log.info("请求30001接口结束");
        }
    }
}