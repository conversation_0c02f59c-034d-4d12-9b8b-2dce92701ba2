package com.psbc.cpufp.client;

import com.psbc.cpufp.util.PortChecker;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.Scanner;

/**
 * 调试版Netty Socket客户端
 * 支持断点调试和手动控制
 */
@Slf4j
public class DebugNettySocketClient {

    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8888;

    public static void main(String[] args) {
        DebugNettySocketClient client = new DebugNettySocketClient();
        Scanner scanner = new Scanner(System.in);
        
        // 检查服务器状态
        log.info("检查Netty服务器是否启动...");
        if (!PortChecker.isPortAvailable(SERVER_HOST, SERVER_PORT)) {
            log.error("Netty服务器未启动，请先启动Spring Boot应用");
            return;
        }
        
        log.info("服务器已启动，进入调试模式");
        log.info("可用的测试消息:");
        log.info("1. 30001接口 (JSON)");
        log.info("2. 20006接口 (XML)");
        log.info("3. 委托方服务 (XML)");
        log.info("4. 自定义消息");
        log.info("0. 退出");
        
        while (true) {
            System.out.print("\n请选择测试类型 (0-4): ");
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    client.test30001Interface();
                    break;
                case "2":
                    client.test20006Interface();
                    break;
                case "3":
                    client.testDelegateService();
                    break;
                case "4":
                    client.testCustomMessage(scanner);
                    break;
                case "0":
                    log.info("退出调试模式");
                    return;
                default:
                    log.warn("无效选择，请输入0-4");
            }
            
            System.out.print("\n按Enter键继续...");
            scanner.nextLine();
        }
    }

    /**
     * 测试30001接口
     */
    public void test30001Interface() {
        String message = "{\"serviceno\":\"30001\",\"data\":\"test json message\"}";
        log.info("=== 测试30001接口 ===");
        sendMessageWithDebug(message, "30001接口");
    }

    /**
     * 测试20006接口
     */
    public void test20006Interface() {
        String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20006</serviceno></head>" +
                "<body><data>test xml message</data></body></content>";
        log.info("=== 测试20006接口 ===");
        sendMessageWithDebug(message, "20006接口");
    }

    /**
     * 测试委托方服务
     */
    public void testDelegateService() {
        String message = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>99999</serviceno></head>" +
                "<body><data>test delegate message</data></body></content>";
        log.info("=== 测试委托方服务 ===");
        sendMessageWithDebug(message, "委托方服务");
    }

    /**
     * 测试自定义消息
     */
    public void testCustomMessage(Scanner scanner) {
        System.out.print("请输入自定义消息: ");
        String message = scanner.nextLine();
        if (message.trim().isEmpty()) {
            log.warn("消息不能为空");
            return;
        }
        log.info("=== 测试自定义消息 ===");
        sendMessageWithDebug(message, "自定义消息");
    }

    /**
     * 带调试信息的消息发送
     */
    public void sendMessageWithDebug(String message, String testName) {
        log.info("开始{}测试", testName);
        log.info("消息内容: {}", message);
        
        Socket socket = null;
        try {
            // 步骤1: 创建连接
            log.info("步骤1: 创建Socket连接...");
            socket = new Socket(SERVER_HOST, SERVER_PORT);
            socket.setSoTimeout(30000);
            socket.setKeepAlive(true);
            socket.setTcpNoDelay(true);
            log.info("连接创建成功: {}", socket);
            
            // 步骤2: 发送消息
            log.info("步骤2: 发送消息...");
            sendFormattedMessageWithDebug(socket, message);
            log.info("消息发送完成");
            
            // 步骤3: 等待响应
            log.info("步骤3: 等待服务器响应...");
            log.info("提示: 如果需要调试，请在此处设置断点");
            
            // 给服务器一些处理时间
            Thread.sleep(500);
            
            // 步骤4: 接收响应
            log.info("步骤4: 接收响应...");
            String response = receiveFormattedMessageWithDebug(socket);
            log.info("响应接收完成");
            log.info("响应内容: {}", response);
            
            // 步骤5: 优雅关闭
            log.info("步骤5: 关闭连接...");
            closeConnectionGracefully(socket);
            log.info("{}测试成功完成", testName);
            
        } catch (Exception e) {
            log.error("{}测试失败", testName, e);
        } finally {
            if (socket != null && !socket.isClosed()) {
                try {
                    socket.close();
                } catch (IOException e) {
                    log.warn("强制关闭连接时发生错误", e);
                }
            }
        }
    }

    /**
     * 带调试信息的消息发送
     */
    private void sendFormattedMessageWithDebug(Socket socket, String message) throws IOException, InterruptedException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;
        
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);
        
        log.info("消息长度: {} 字节", messageLength);
        log.info("长度字段: {}", lengthStr);
        
        OutputStream out = socket.getOutputStream();
        
        // 发送长度字段
        log.debug("发送长度字段...");
        out.write(lengthBytes);
        out.flush();
        log.debug("长度字段发送完成");
        
        // 短暂等待
        Thread.sleep(50);
        
        // 发送消息体
        log.debug("发送消息体...");
        out.write(messageBytes);
        out.flush();
        log.debug("消息体发送完成");
        
        // 确保数据发送完成
        Thread.sleep(100);
        log.info("所有数据发送完成");
    }

    /**
     * 带调试信息的消息接收
     */
    private String receiveFormattedMessageWithDebug(Socket socket) throws IOException {
        InputStream in = socket.getInputStream();
        
        // 检查输入流状态
        log.debug("检查输入流可用字节数: {}", in.available());
        
        // 读取长度字段
        log.debug("读取长度字段...");
        byte[] lengthBytes = new byte[6];
        int totalRead = 0;
        
        while (totalRead < 6) {
            int bytesRead = in.read(lengthBytes, totalRead, 6 - totalRead);
            if (bytesRead == -1) {
                throw new IOException("连接在读取长度字段时关闭");
            }
            totalRead += bytesRead;
            log.debug("长度字段读取进度: {}/6", totalRead);
        }
        
        String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
        log.info("接收到长度字段: {}", lengthStr);
        
        int messageLength = Integer.parseInt(lengthStr);
        log.info("消息体长度: {} 字节", messageLength);
        
        // 读取消息体
        log.debug("读取消息体...");
        byte[] messageBytes = new byte[messageLength];
        totalRead = 0;
        
        while (totalRead < messageLength) {
            int bytesRead = in.read(messageBytes, totalRead, messageLength - totalRead);
            if (bytesRead == -1) {
                throw new IOException("连接在读取消息体时关闭");
            }
            totalRead += bytesRead;
            log.debug("消息体读取进度: {}/{}", totalRead, messageLength);
        }
        
        String result = new String(messageBytes, StandardCharsets.UTF_8);
        log.info("消息体接收完成，长度: {}", result.length());
        return result;
    }

    /**
     * 优雅关闭连接
     */
    private void closeConnectionGracefully(Socket socket) throws IOException {
        if (socket != null && !socket.isClosed()) {
            log.debug("开始优雅关闭连接...");
            
            // 关闭输出流
            if (!socket.isOutputShutdown()) {
                socket.shutdownOutput();
                log.debug("输出流已关闭");
            }
            
            // 等待服务器关闭连接
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 关闭连接
            socket.close();
            log.debug("连接已关闭");
        }
    }
}
