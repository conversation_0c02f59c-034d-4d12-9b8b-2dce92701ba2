package com.psbc.cpufp.client;

import com.psbc.cpufp.util.PortChecker;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * Netty Socket客户端测试类
 * 用于测试重构后的Netty服务器
 */
@Slf4j
public class NettySocketClient {

    private static final String SERVER_HOST = "127.0.0.1";
    private static final int SERVER_PORT = 8888;

    public static void main(String[] args) {
        NettySocketClient client = new NettySocketClient();

        // 首先检查服务器是否启动
        log.info("检查Netty服务器是否启动...");
        if (!PortChecker.isPortAvailable(SERVER_HOST, SERVER_PORT)) {
            log.error("Netty服务器未启动，请先启动Spring Boot应用");
            log.info("等待服务器启动...");
            if (!PortChecker.waitForPort(SERVER_HOST, SERVER_PORT, 30000)) {
                log.error("等待服务器启动超时，请检查服务器配置");
                return;
            }
        }

        log.info("服务器已启动，开始测试...");

//        // 测试JSON格式消息（服务号30001）
//        String jsonMessage = "{\"serviceno\":\"30001\",\"data\":\"test json message\"}";
//        client.sendMessage(jsonMessage);

        // 测试XML格式消息（服务号20006）
        String xmlMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20006</serviceno></head>" +
                "<body><data>test xml message</data></body></content>";
        client.sendMessage(xmlMessage);

        // 测试委托方服务（其他服务号）
        String delegateMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content>" +
                "<head>" +
                    "<serviceno>20002</serviceno>" +
                    "<usr>xmjydj</usr>" +
                    "<pwd></pwd>" +
                    "<optname></optname>" +
                    "<signmsg></signmsg>" +
                "</head>" +
                "<body>" +
                    "<serialno>XXX</serialno>" +
                    "<accountname>XXX</accountname>" +
                    "<accountno>XXX</accountno>" +
                    "<executetype>XXX</executetype>" +
                    "<executeamount>XXX</executeamount>" +
                    "<executedept>XXX</executedept>" +
                    "<executedate>XXX</executedate>" +
                    "<releaseserialno>XXX</releaseserialno>" +
                    "<releasetime>XXX</releasetime>" +
                    "<note>XXX</note>" +
                "</body>" +
                "</content>";
        client.sendMessage(delegateMessage);
    }

    /**
     * 发送消息到服务器
     * @param message 要发送的消息内容
     */
    public void sendMessage(String message) {
        Socket socket = null;
        try {
            socket = new Socket(SERVER_HOST, SERVER_PORT);
            socket.setSoTimeout(30000); // 设置30秒超时
            socket.setKeepAlive(true);   // 保持连接活跃

            log.info("连接到服务器: {}:{}", SERVER_HOST, SERVER_PORT);

            // 发送消息
            sendFormattedMessage(socket, message);
            log.info("消息发送完成，等待响应...");

            // 等待一小段时间确保服务器处理完成
            Thread.sleep(100);

            // 接收响应
            String response = receiveFormattedMessage(socket);
            log.info("收到响应: {}", response);

            // 等待一段时间再关闭连接
            Thread.sleep(500);

        } catch (Exception e) {
            log.error("发送消息失败", e);
        } finally {
            if (socket != null && !socket.isClosed()) {
                try {
                    socket.close();
                    log.info("连接已关闭");
                } catch (Exception e) {
                    log.warn("关闭连接时发生错误", e);
                }
            }
        }
    }

    /**
     * 发送格式化的消息（添加6位长度前缀）
     */
    private void sendFormattedMessage(Socket socket, String message) throws IOException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;

        // 格式化长度字段为6位字符串
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);

        log.info("发送消息，长度: {}, 内容: {}", messageLength, message);

        OutputStream out = socket.getOutputStream();

        // 发送长度字段
        out.write(lengthBytes);
        out.flush();
        log.debug("长度字段发送完成: {}", lengthStr);

        // 发送消息体
        out.write(messageBytes);
        out.flush();
        log.debug("消息体发送完成");

        // 确保数据发送完成
        try {
            Thread.sleep(50); // 等待50ms确保数据发送完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 接收格式化的消息（解析6位长度前缀）
     */
    private String receiveFormattedMessage(Socket socket) throws IOException {
        InputStream in = socket.getInputStream();

        // 读取长度字段（6字节）
        byte[] lengthBytes = new byte[6];
        int totalBytesRead = 0;

        // 确保读取完整的长度字段
        while (totalBytesRead < 6) {
            int bytesRead = in.read(lengthBytes, totalBytesRead, 6 - totalBytesRead);
            if (bytesRead == -1) {
                throw new IOException("连接在读取长度字段时关闭");
            }
            totalBytesRead += bytesRead;
        }

        String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
        int messageLength;
        try {
            messageLength = Integer.parseInt(lengthStr);
            log.info("响应消息长度: {}", messageLength);
        } catch (NumberFormatException e) {
            throw new IOException("无法解析长度字段: " + lengthStr);
        }

        if (messageLength <= 0 || messageLength > 1024 * 1024) { // 限制最大1MB
            throw new IOException("消息长度异常: " + messageLength);
        }

        // 读取消息体
        byte[] messageBytes = new byte[messageLength];
        totalBytesRead = 0;

        while (totalBytesRead < messageLength) {
            int currentBytesRead = in.read(messageBytes, totalBytesRead, messageLength - totalBytesRead);
            if (currentBytesRead == -1) {
                throw new IOException("连接在读取消息体时关闭，已读取: " + totalBytesRead + "/" + messageLength);
            }
            totalBytesRead += currentBytesRead;
        }

        String result = new String(messageBytes, StandardCharsets.UTF_8);
        log.info("成功接收完整消息，长度: {}", result.length());
        return result;
    }
}
