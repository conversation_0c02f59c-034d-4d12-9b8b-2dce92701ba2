package com.psbc.cpufp.config;

import com.psbc.cpufp.service.IWebService;
import com.psbc.cpufp.service.impl.UserServiceImpl;
import com.psbc.cpufp.service.impl.WebServiceImpl;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.xml.ws.Endpoint;

@Configuration
public class WebServiceConfig {

    @Bean
    public ServletRegistrationBean<CXFServlet> cxfServlet() {
        return new ServletRegistrationBean<>(new CXFServlet(), "/wservices/*");
    }

    @Bean
    public IWebService webService() {
        return new WebServiceImpl();
    }

    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        return new SpringBus();
    }
    @Bean
    public Endpoint endpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), webService());
        endpoint.publish("/IWebServiceService");
        return endpoint;
    }

    // 发布到 /services/userService
    @Bean
    public Endpoint userServiceEndpoint(UserServiceImpl impl) {
        EndpointImpl endpoint = new EndpointImpl(springBus(), impl);
        endpoint.publish("/userService");
        return endpoint;
    }
}