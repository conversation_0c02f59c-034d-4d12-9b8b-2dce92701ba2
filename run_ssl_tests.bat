@echo off
echo ========================================
echo SSL/TLS协议测试工具
echo ========================================
echo.

echo 编译测试类...
mvn test-compile

if %ERRORLEVEL% neq 0 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)

echo.
echo ========================================
echo 选择要运行的测试:
echo ========================================
echo 1. SSL协议兼容性测试 (基础HTTP连接)
echo 2. WebService SSL测试 (CXF客户端)
echo 3. SSL诊断工具 (详细分析)
echo 4. 运行所有测试
echo 5. 退出
echo ========================================
set /p choice=请选择 (1-5): 

if "%choice%"=="1" goto test1
if "%choice%"=="2" goto test2
if "%choice%"=="3" goto test3
if "%choice%"=="4" goto testall
if "%choice%"=="5" goto end
goto invalid

:test1
echo.
echo 运行SSL协议兼容性测试...
echo ========================================
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.SSLProtocolTestClient" -Dexec.classpathScope=test
goto end

:test2
echo.
echo 运行WebService SSL测试...
echo ========================================
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.WebServiceSSLTestClient" -Dexec.classpathScope=test
goto end

:test3
echo.
echo 运行SSL诊断工具...
echo ========================================
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.SSLDiagnosticTool" -Dexec.classpathScope=test
goto end

:testall
echo.
echo 运行所有SSL测试...
echo ========================================
echo.
echo [1/3] SSL协议兼容性测试
echo ----------------------------------------
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.SSLProtocolTestClient" -Dexec.classpathScope=test

echo.
echo [2/3] WebService SSL测试
echo ----------------------------------------
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.WebServiceSSLTestClient" -Dexec.classpathScope=test

echo.
echo [3/3] SSL诊断工具
echo ----------------------------------------
mvn exec:java -Dexec.mainClass="com.psbc.cpufp.ssl.SSLDiagnosticTool" -Dexec.classpathScope=test

echo.
echo 所有测试完成!
goto end

:invalid
echo 无效选择，请重新运行脚本
goto end

:end
echo.
echo ========================================
echo 测试完成
echo ========================================
pause
