package com.psbc.cpufp.httpApplication;

import com.psbc.cpufp.config.XmysfzjjgProperties;
import com.sun.net.httpserver.HttpServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;

/**
 * HTTP转发服务
 * 负责启动独立的HTTP服务器，接收HTTP请求并转发到Spring Boot应用
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HttpForwardingService {
    
    private final XmysfzjjgProperties xmysfzjjgProperties;
    
    @Value("${server.port}")
    private String serverPort;
    
    @Value("${server.servlet.context-path}")
    private String contextPath;
    
    private HttpServer httpServer;
    
    @PostConstruct
    public void startHttpService() {
        Integer port = xmysfzjjgProperties.getHttpPort();
        if (port == null) {
            log.warn("HTTP转发服务端口未配置，跳过启动");
            return;
        }
        
        try {
            // 构建Spring Boot应用的基础URL (使用HTTPS)
            String springBootBaseUrl = "https://127.0.0.1:" + serverPort + contextPath;
            
            // 获取配置的请求前缀
            String requestPrefix = xmysfzjjgProperties.getHttpRequestPrefix();
            if (requestPrefix == null) {
                requestPrefix = "";
            }
            
            // 确保前缀格式正确（以/开头，不以/结尾）
            if (!requestPrefix.isEmpty()) {
                if (!requestPrefix.startsWith("/")) {
                    requestPrefix = "/" + requestPrefix;
                }
                if (requestPrefix.endsWith("/")) {
                    requestPrefix = requestPrefix.substring(0, requestPrefix.length() - 1);
                }
            }
            
            log.info("Spring Boot应用基础URL: {}", springBootBaseUrl);
            log.info("HTTP转发服务请求前缀: {}", requestPrefix.isEmpty() ? "无" : requestPrefix);
            
            httpServer = HttpServer.create(new InetSocketAddress(port), 0);
            
            // 添加XZP通用处理器，处理所有/xzp路径的请求
            String xzpContextPath = requestPrefix + "/xzp";
            httpServer.createContext(xzpContextPath, new XzpUniversalHandler(springBootBaseUrl));
            
            // 保留原有的特定路径处理器（如果需要）
            httpServer.createContext("/api/admin/xzp/queryYxjfLsxdye", new MyHttpHandler());
            
            httpServer.setExecutor(Executors.newFixedThreadPool(10));
            httpServer.start();
            
            log.info("HTTP转发服务启动成功，端口：{}", port);
            log.info("HTTP服务已启动，可通过以下地址访问:");
            log.info("  - http://127.0.0.1:{}{}/xzp/queryYxjfLsxdye (POST)", port, requestPrefix);
            log.info("  - http://127.0.0.1:{}{}/xzp/test (POST)", port, requestPrefix);
            log.info("  - http://127.0.0.1:{}{}/xzp/testGet (GET)", port, requestPrefix);
            
        } catch (Exception e) {
            log.error("HTTP转发服务启动失败: {}", e.getMessage(), e);
        }
    }
    
    @PreDestroy
    public void stopHttpService() {
        if (httpServer != null) {
            try {
                httpServer.stop(0);
                log.info("HTTP转发服务已关闭");
            } catch (Exception e) {
                log.error("关闭HTTP转发服务时发生错误: {}", e.getMessage(), e);
            }
        }
    }
}