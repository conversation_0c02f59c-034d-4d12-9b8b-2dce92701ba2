package com.psbc.cpufp.ssl;

import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.frontend.ClientProxy;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.cxf.transport.http.HTTPConduit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import java.net.URL;
import java.security.cert.X509Certificate;

/**
 * WebService SSL/TLS协议测试客户端
 * 专门测试CXF WebService客户端的SSL连接和协议不一致问题
 */
public class WebServiceSSLTestClient {
    
    private static final Logger log = LoggerFactory.getLogger(WebServiceSSLTestClient.class);
    
    // 测试目标
    private static final String WSDL_URL = "https://188.9.30.65:8443/xmysfzjjg/wservices/IWebServiceService?wsdl";
    private static final String SERVICE_URL = "https://188.9.30.65:8443/xmysfzjjg/wservices/IWebServiceService";
    
    public static void main(String[] args) {
        WebServiceSSLTestClient client = new WebServiceSSLTestClient();
        
        log.info("开始WebService SSL/TLS协议兼容性测试...");
        log.info("WSDL URL: {}", WSDL_URL);
        log.info("Service URL: {}", SERVICE_URL);
        
        // 测试不同的TLS配置
        client.testDefaultSSLConfig();
        client.testTLS12Config();
        client.testTLS13Config();
        client.testLegacyTLSConfig();
        client.testStrictSSLConfig();
        client.testCustomTrustManager();
    }
    
    /**
     * 测试默认SSL配置
     */
    public void testDefaultSSLConfig() {
        log.info("=== 测试默认SSL配置 ===");
        
        try {
            // 使用默认配置创建WebService客户端
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(TestWebServiceInterface.class);
            factory.setAddress(SERVICE_URL);
            
            TestWebServiceInterface client = (TestWebServiceInterface) factory.create();
            
            // 测试WSDL访问
            testWSDLAccess(WSDL_URL);
            
            log.info("默认SSL配置 - 客户端创建成功");
            
        } catch (Exception e) {
            log.error("默认SSL配置 - 失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试TLS 1.2配置
     */
    public void testTLS12Config() {
        log.info("=== 测试TLS 1.2配置 ===");
        
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(TestWebServiceInterface.class);
            factory.setAddress(SERVICE_URL);
            
            TestWebServiceInterface client = (TestWebServiceInterface) factory.create();
            
            // 配置TLS 1.2
            configureTLS(client, "TLSv1.2", true);
            
            log.info("TLS 1.2配置 - 客户端创建成功");
            
        } catch (Exception e) {
            log.error("TLS 1.2配置 - 失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试TLS 1.3配置
     */
    public void testTLS13Config() {
        log.info("=== 测试TLS 1.3配置 ===");
        
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(TestWebServiceInterface.class);
            factory.setAddress(SERVICE_URL);
            
            TestWebServiceInterface client = (TestWebServiceInterface) factory.create();
            
            // 配置TLS 1.3
            configureTLS(client, "TLSv1.3", true);
            
            log.info("TLS 1.3配置 - 客户端创建成功");
            
        } catch (Exception e) {
            log.error("TLS 1.3配置 - 失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试旧版TLS配置（模拟协议不一致）
     */
    public void testLegacyTLSConfig() {
        log.info("=== 测试旧版TLS配置（协议不一致） ===");
        
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(TestWebServiceInterface.class);
            factory.setAddress(SERVICE_URL);
            
            TestWebServiceInterface client = (TestWebServiceInterface) factory.create();
            
            // 配置旧版TLS（可能导致协议不一致）
            configureTLS(client, "TLSv1", true);
            
            log.info("旧版TLS配置 - 客户端创建成功（可能在实际调用时失败）");
            
        } catch (Exception e) {
            log.error("旧版TLS配置 - 预期失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试严格SSL配置（启用证书验证）
     */
    public void testStrictSSLConfig() {
        log.info("=== 测试严格SSL配置（启用证书验证） ===");
        
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(TestWebServiceInterface.class);
            factory.setAddress(SERVICE_URL);
            
            TestWebServiceInterface client = (TestWebServiceInterface) factory.create();
            
            // 配置严格SSL（不信任所有证书）
            configureTLS(client, "TLSv1.2", false);
            
            log.info("严格SSL配置 - 客户端创建成功（可能在证书验证时失败）");
            
        } catch (Exception e) {
            log.error("严格SSL配置 - 预期失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试自定义TrustManager
     */
    public void testCustomTrustManager() {
        log.info("=== 测试自定义TrustManager ===");
        
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(TestWebServiceInterface.class);
            factory.setAddress(SERVICE_URL);
            
            TestWebServiceInterface client = (TestWebServiceInterface) factory.create();
            
            // 配置自定义TrustManager
            configureCustomTrustManager(client);
            
            log.info("自定义TrustManager - 客户端创建成功");
            
        } catch (Exception e) {
            log.error("自定义TrustManager - 失败: {}", e.getMessage());
        }
    }
    
    /**
     * 配置TLS参数
     */
    private void configureTLS(Object client, String protocol, boolean trustAll) {
        try {
            Client cxfClient = ClientProxy.getClient(client);
            HTTPConduit httpConduit = (HTTPConduit) cxfClient.getConduit();
            
            TLSClientParameters tlsParams = new TLSClientParameters();
            tlsParams.setSecureSocketProtocol(protocol);
            
            if (trustAll) {
                // 信任所有证书
                tlsParams.setTrustManagers(new TrustManager[] {
                    new X509TrustManager() {
                        public void checkClientTrusted(X509Certificate[] chain, String authType) {}
                        public void checkServerTrusted(X509Certificate[] chain, String authType) {}
                        public X509Certificate[] getAcceptedIssuers() { return null; }
                    }
                });
                tlsParams.setDisableCNCheck(true);
            }
            
            httpConduit.setTlsClientParameters(tlsParams);
            
            log.info("TLS配置完成 - 协议: {}, 信任所有证书: {}", protocol, trustAll);
            
        } catch (Exception e) {
            log.error("TLS配置失败: {}", e.getMessage());
            throw new RuntimeException("TLS配置失败", e);
        }
    }
    
    /**
     * 配置自定义TrustManager
     */
    private void configureCustomTrustManager(Object client) {
        try {
            Client cxfClient = ClientProxy.getClient(client);
            HTTPConduit httpConduit = (HTTPConduit) cxfClient.getConduit();
            
            TLSClientParameters tlsParams = new TLSClientParameters();
            tlsParams.setSecureSocketProtocol("TLSv1.2");
            
            // 自定义TrustManager，记录证书信息
            tlsParams.setTrustManagers(new TrustManager[] {
                new X509TrustManager() {
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                        log.info("检查客户端证书 - 认证类型: {}", authType);
                    }
                    
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                        log.info("检查服务器证书 - 认证类型: {}", authType);
                        if (chain != null && chain.length > 0) {
                            X509Certificate cert = chain[0];
                            log.info("服务器证书主题: {}", cert.getSubjectDN());
                            log.info("服务器证书颁发者: {}", cert.getIssuerDN());
                            log.info("服务器证书有效期: {} 到 {}", cert.getNotBefore(), cert.getNotAfter());
                        }
                    }
                    
                    public X509Certificate[] getAcceptedIssuers() { 
                        return new X509Certificate[0]; 
                    }
                }
            });
            
            tlsParams.setDisableCNCheck(true);
            httpConduit.setTlsClientParameters(tlsParams);
            
        } catch (Exception e) {
            log.error("自定义TrustManager配置失败: {}", e.getMessage());
            throw new RuntimeException("自定义TrustManager配置失败", e);
        }
    }
    
    /**
     * 测试WSDL访问
     */
    private void testWSDLAccess(String wsdlUrl) {
        try {
            log.info("测试WSDL访问: {}", wsdlUrl);
            
            URL url = new URL(wsdlUrl);
            QName serviceName = new QName("http://service.cpufp.psbc.com/", "IWebServiceService");
            Service service = Service.create(url, serviceName);
            
            log.info("WSDL访问成功 - 服务名: {}", service.getServiceName());
            
        } catch (Exception e) {
            log.error("WSDL访问失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试用的WebService接口
     */
    public interface TestWebServiceInterface {
        // 这里可以定义一些测试方法
        String testMethod(String input);
    }
}
