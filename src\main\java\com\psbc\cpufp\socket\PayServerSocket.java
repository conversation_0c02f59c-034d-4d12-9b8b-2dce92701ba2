package com.psbc.cpufp.socket;

import com.psbc.cpufp.config.PayServerInitializer;
import com.psbc.cpufp.config.XmysfzjjgProperties;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

@Slf4j
@Component
@RequiredArgsConstructor
public class PayServerSocket {

  private final XmysfzjjgProperties properties;
  private final PayServerInitializer initializer;

  private EventLoopGroup bossGroup;
  private EventLoopGroup workerGroup;
  private ChannelFuture channelFuture;

  @PostConstruct
  public void init() {
    // 在Spring容器初始化完成后自动启动Netty服务器
    new Thread(this::start, "netty-server-starter").start();
  }

  public void start() {
    Integer port = properties.getWgPort();
    if (port == null) {
      log.error("Socket服务器端口未配置");
      return;
    }

    bossGroup = new NioEventLoopGroup(1);
    workerGroup = new NioEventLoopGroup();

    try {
      ServerBootstrap bootstrap = new ServerBootstrap()
              .group(bossGroup, workerGroup)
              .channel(NioServerSocketChannel.class)
              .childHandler(initializer);

      channelFuture = bootstrap.bind(port).sync();
      log.info("Netty Socket服务器启动成功，监听端口: {}", port);

      // 等待服务器关闭
      channelFuture.channel().closeFuture().sync();

    } catch (Exception e) {
      log.error("Netty Socket服务器启动失败", e);
      stopServer();
    }
  }

  @PreDestroy
  public void stopServer() {
    try {
      if (channelFuture != null) {
        channelFuture.channel().close().sync();
      }
    } catch (Exception e) {
      log.error("关闭服务器通道时发生错误", e);
    } finally {
      if (bossGroup != null) {
        bossGroup.shutdownGracefully();
      }
      if (workerGroup != null) {
        workerGroup.shutdownGracefully();
      }
      log.info("Netty Socket服务器已关闭");
    }
  }
}