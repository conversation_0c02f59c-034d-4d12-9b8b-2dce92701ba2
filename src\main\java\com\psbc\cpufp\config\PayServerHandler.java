package com.psbc.cpufp.config;

import com.psbc.cpufp.service.PaySocketService;
import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Sharable
@RequiredArgsConstructor
public class PayServerHandler extends ChannelInboundHandlerAdapter {

    private final PaySocketService paySocketService;

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.info("客户端连接成功：{}", ctx.channel().remoteAddress());
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        try {
            String message = (String) msg;
            log.info("收到消息：{}", message);

            // 使用PaySocketService处理业务逻辑
            String response = paySocketService.processMessage(message);

            // 发送响应
            ctx.writeAndFlush(response);
            log.info("响应已发送");

        } catch (Exception e) {
            log.error("处理消息时发生错误", e);
            // 发送错误响应
            String errorResponse = "处理消息时发生错误: " + e.getMessage();
            ctx.writeAndFlush(errorResponse);
        }
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) {
        ctx.flush();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("连接异常", cause);
        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.info("客户端断开连接：{}", ctx.channel().remoteAddress());
    }
}