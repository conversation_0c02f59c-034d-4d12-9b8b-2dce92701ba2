package com.psbc.cpufp.httpApplication;

import cn.hutool.http.HttpRequest;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

/**
 * XZP通用HTTP处理器
 * 处理所有/xzp路径下的请求，转发到Spring Boot应用的对应接口
 */
@Slf4j
public class XzpUniversalHandler implements HttpHandler {
    
    private final String springBootBaseUrl;
    
    public XzpUniversalHandler(String springBootBaseUrl) {
        this.springBootBaseUrl = springBootBaseUrl;
    }
    
    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String requestPath = exchange.getRequestURI().getPath();
        String requestMethod = exchange.getRequestMethod();
        
        log.info("收到HTTP请求: {} {}", requestMethod, requestPath);
        
        try {
            // 将HTTP转发服务的路径转换为Spring Boot应用的路径
            // 例如: /httpapi/xzp/test -> /xzp/test
            // 或者: /api/v1/httpapi/xzp/test -> /xzp/test (如果配置了前缀)
            String springBootPath = convertToSpringBootPath(requestPath);
            String targetUrl = springBootBaseUrl + springBootPath;
            
            log.info("转发路径映射: {} -> {}", requestPath, springBootPath);
            log.info("目标URL: {}", targetUrl);
            
            String responseBody;
            
            if ("POST".equalsIgnoreCase(requestMethod)) {
                // 读取请求体
                String requestBody = readRequestBody(exchange);
                log.info("请求体: {}", requestBody);
                
                // 转发POST请求
                responseBody = HttpRequest.post(targetUrl)
                        .header("Content-Type", "application/json")
                        .body(requestBody)
                        .execute()
                        .body();
            } else if ("GET".equalsIgnoreCase(requestMethod)) {
                // 转发GET请求
                responseBody = HttpRequest.get(targetUrl)
                        .execute()
                        .body();
            } else {
                responseBody = "不支持的请求方法: " + requestMethod;
                exchange.sendResponseHeaders(405, responseBody.getBytes(StandardCharsets.UTF_8).length);
                writeResponse(exchange, responseBody);
                return;
            }
            
            log.info("转发响应: {}", responseBody);
            
            // 发送响应
            exchange.sendResponseHeaders(200, responseBody.getBytes(StandardCharsets.UTF_8).length);
            writeResponse(exchange, responseBody);
            
        } catch (Exception e) {
            log.error("处理请求异常: {}", e.getMessage(), e);
            String errorResponse = "请求处理异常: " + e.getMessage();
            exchange.sendResponseHeaders(500, errorResponse.getBytes(StandardCharsets.UTF_8).length);
            writeResponse(exchange, errorResponse);
        }
    }
    
    /**
     * 将HTTP转发服务的路径转换为Spring Boot应用的路径
     * 移除前缀和/httpapi部分，保留/xzp及后续路径
     * 
     * @param requestPath HTTP转发服务接收到的请求路径
     * @return Spring Boot应用对应的路径
     */
    private String convertToSpringBootPath(String requestPath) {
        // 查找/httpapi/xzp的位置
        int httpApiIndex = requestPath.indexOf("/httpapi/xzp");
        if (httpApiIndex != -1) {
            // 提取/xzp及后续路径
            return requestPath.substring(httpApiIndex + "/httpapi".length());
        }
        
        // 如果没有找到/httpapi/xzp，直接返回原路径（兼容性处理）
        log.warn("未找到预期的/httpapi/xzp路径模式，原路径: {}", requestPath);
        return requestPath;
    }
    
    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            StringBuilder requestBody = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
            return requestBody.toString();
        }
    }
    
    /**
     * 写入响应
     */
    private void writeResponse(HttpExchange exchange, String response) throws IOException {
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(response.getBytes(StandardCharsets.UTF_8));
        }
    }
}