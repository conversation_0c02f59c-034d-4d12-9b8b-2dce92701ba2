package com.psbc.cpufp.service.impl;


import com.psbc.cpufp.config.XmysfzjjgProperties;
import com.psbc.cpufp.service.IWebService;
import com.psbc.cpufp.util.Wlhost;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import java.io.IOException;

@Slf4j
@WebService(serviceName = "IWebServiceService",
           targetNamespace = "http://webservice.psbc.com/",
           endpointInterface = "com.psbc.cpufp.service.IWebService")
public class WebServiceImpl implements IWebService {

    @WebMethod
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    public String sayHello(@WebParam(targetNamespace = "http://webservice.psbc.com/", name = "arg0") String s) {
        return "Welcome ! " + s;
    }
    @Autowired
    private XmysfzjjgProperties xmysfzjjgProperties;

    @WebMethod(operationName = "Execute", action = "http://webservice.psbc.com/Execute")
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    public String execute(
            @WebParam(targetNamespace = "http://webservice.psbc.com/", name = "arg0") String BankID,
            @WebParam(name = "arg1") String inParmeter) throws IOException {
        String rspmsg = null;
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>接收委托方webservice报文开始>>>>>>>>>>>>>>>>>>>>>>>>>");
        log.info("BankID:" + BankID + " 报文inParmeter:"+ inParmeter.trim());
        Wlhost wl = new Wlhost();
        String ip = xmysfzjjgProperties.getIp();
        Integer port = xmysfzjjgProperties.getPort();
        long start = System.nanoTime();
        log.info("报文发往外联系统开始>>>");
        rspmsg = wl.sendToWl(ip, port, inParmeter.trim());
        long end = System.nanoTime();
        log.info("报文发往外联系统结束>>>");
        log.info("外联系统返回报文耗时:"+ ((end - start) / 1000L / 1000L) + "毫秒");
        log.info("外联系统返回报文长度"+ rspmsg.length());
        log.info("外联系统返回报文:["+ rspmsg + "]");
        return rspmsg;
    }
}
