# 项目打包和部署指南

## 📦 打包结果

项目已成功打包，生成以下文件：

### 生成的JAR文件
```
target/
├── agent-spf.jar              # 可执行JAR文件 (83MB)
├── agent-spf-sources.jar      # 源码JAR文件 (104KB)
└── agent-spf.jar.original     # 原始JAR文件（不含依赖）
```

### 主要JAR文件信息
- **文件名**: `agent-spf.jar`
- **大小**: 约83MB
- **类型**: Spring Boot可执行JAR（包含所有依赖）
- **Java版本**: 兼容Java 11+

## 🚀 启动方式

### 方式1: 使用启动脚本（推荐）
```bash
# Windows
start-app.bat

# 或者直接双击 start-app.bat 文件
```

### 方式2: 手动启动
```bash
# 基本启动
java -jar target/agent-spf.jar

# 指定配置环境
java -jar target/agent-spf.jar --spring.profiles.active=local

# 指定端口和配置
java -jar target/agent-spf.jar --spring.profiles.active=local --server.port=6666

# 带JVM参数启动
java -Xms512m -Xmx1024m -XX:+UseG1GC -jar target/agent-spf.jar --spring.profiles.active=local
```

### 方式3: 后台启动（Linux/Unix）
```bash
# 后台启动
nohup java -jar target/agent-spf.jar --spring.profiles.active=local > app.log 2>&1 &

# 查看进程
ps aux | grep agent-spf

# 停止进程
kill -9 <PID>
```

## ⚙️ 配置环境

### 可用的配置环境
- **local**: 本地开发环境（默认端口6666）
- **dev**: 开发测试环境
- **master**: 生产环境

### 配置文件位置
```
src/main/resources/
├── application.yml          # 默认配置
├── local/application.yml    # 本地环境配置
├── dev/application.yml      # 开发环境配置
└── master/application.yml   # 生产环境配置
```

## 🔧 JVM参数建议

### 开发环境
```bash
-Xms512m -Xmx1024m -XX:+UseG1GC
```

### 生产环境
```bash
-Xms1g -Xmx2g -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/logs/
```

### 调试模式
```bash
-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005
```

## 📋 服务端口

| 服务类型 | 端口 | 说明 |
|---------|------|------|
| HTTP/WebService | 6666 | 主要Web服务端口 |
| Socket服务 | 8888 | Netty Socket通信端口 |
| 外联系统 | 9702 | 外联系统通信端口 |

## ✅ 启动验证

### 1. 检查端口监听
```bash
# Windows
netstat -ano | findstr :6666
netstat -ano | findstr :8888

# Linux
netstat -tulpn | grep :6666
netstat -tulpn | grep :8888
```

### 2. 检查WebService
```bash
curl http://localhost:6666/xmysfzjjg/wservices/IWebServiceService?wsdl
```

### 3. 检查健康状态
```bash
curl http://localhost:6666/actuator/health
```

### 4. 检查应用信息
```bash
curl http://localhost:6666/actuator/info
```

## 🐛 常见问题

### 1. 端口被占用
```bash
# 查找占用端口的进程
netstat -ano | findstr :6666

# 杀死进程（Windows）
taskkill /PID <PID> /F

# 杀死进程（Linux）
kill -9 <PID>
```

### 2. 内存不足
```bash
# 增加堆内存
java -Xms1g -Xmx2g -jar target/agent-spf.jar
```

### 3. 配置文件找不到
```bash
# 确保配置文件在JAR包内
jar -tf target/agent-spf.jar | grep application.yml

# 使用外部配置文件
java -jar target/agent-spf.jar --spring.config.location=file:./config/application.yml
```

### 4. WebService启动失败
确保已添加JAX-WS依赖：
```xml
<dependency>
    <groupId>javax.xml.ws</groupId>
    <artifactId>jaxws-api</artifactId>
    <version>2.3.1</version>
</dependency>
```

## 📁 部署目录结构

### 推荐的部署目录结构
```
/app/agent-spf/
├── agent-spf.jar           # 应用JAR文件
├── start.sh               # 启动脚本
├── stop.sh                # 停止脚本
├── config/                # 外部配置目录
│   └── application.yml    # 外部配置文件
├── logs/                  # 日志目录
└── backup/                # 备份目录
```

### 部署脚本示例
```bash
#!/bin/bash
# deploy.sh

APP_NAME="agent-spf"
APP_JAR="agent-spf.jar"
APP_DIR="/app/${APP_NAME}"
BACKUP_DIR="${APP_DIR}/backup"

# 创建备份
if [ -f "${APP_DIR}/${APP_JAR}" ]; then
    cp "${APP_DIR}/${APP_JAR}" "${BACKUP_DIR}/${APP_JAR}.$(date +%Y%m%d_%H%M%S)"
fi

# 复制新版本
cp target/${APP_JAR} ${APP_DIR}/

# 重启服务
${APP_DIR}/stop.sh
${APP_DIR}/start.sh
```

## 🔄 更新部署

### 1. 停止服务
```bash
# 找到进程ID
ps aux | grep agent-spf

# 停止服务
kill -15 <PID>  # 优雅停止
# 或
kill -9 <PID>   # 强制停止
```

### 2. 备份当前版本
```bash
cp agent-spf.jar agent-spf.jar.backup.$(date +%Y%m%d_%H%M%S)
```

### 3. 部署新版本
```bash
cp target/agent-spf.jar ./
```

### 4. 启动新版本
```bash
./start-app.bat
# 或
java -jar agent-spf.jar --spring.profiles.active=local
```

## 📊 监控和日志

### 1. 应用监控
- **Actuator端点**: http://localhost:6666/actuator
- **健康检查**: http://localhost:6666/actuator/health
- **指标监控**: http://localhost:6666/actuator/metrics

### 2. 日志配置
```yaml
logging:
  level:
    com.psbc.cpufp: DEBUG
  file:
    name: logs/agent-spf.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### 3. 日志文件位置
- **控制台输出**: 实时日志
- **文件日志**: logs/agent-spf.log
- **错误日志**: logs/error.log

## 🔐 安全配置

### 1. 生产环境建议
- 使用非root用户运行
- 配置防火墙规则
- 启用HTTPS（如需要）
- 配置访问控制

### 2. 配置加密
```yaml
# 使用jasypt加密敏感配置
spring:
  datasource:
    password: ENC(encrypted_password)
```

## 📞 技术支持

如遇到部署问题，请检查：
1. Java版本是否兼容（需要Java 11+）
2. 端口是否被占用
3. 配置文件是否正确
4. 依赖是否完整
5. 日志中的错误信息

更多技术文档请参考项目中的其他MD文件。
