package com.psbc.cpufp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "xmysfzjjg")
public class XmysfzjjgProperties {
    private String ip;
    private Integer port;
    private Integer wgPort;
    private Integer httpPort;
    private String message;
    
    /**
     * HTTP转发服务的请求前缀
     * 例如: "/api/v1", "/branch-agent", "/service" 等
     * 默认为空字符串，表示不添加前缀
     */
    private String httpRequestPrefix = "";
}