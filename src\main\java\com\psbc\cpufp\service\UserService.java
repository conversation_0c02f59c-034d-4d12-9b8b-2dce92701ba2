package com.psbc.cpufp.service;

import com.psbc.cpufp.entity.vo.UserDto;
import org.springframework.stereotype.Service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.util.List;

@WebService (name="UserService", targetNamespace="http://webservice.example.com/")public interface UserService {
    @WebMethod(operationName="getUserById")
    UserDto getUserById(@WebParam(name="id") Integer id);

    @WebMethod(operationName="listUsers")
    List<UserDto> listUsers();
}
