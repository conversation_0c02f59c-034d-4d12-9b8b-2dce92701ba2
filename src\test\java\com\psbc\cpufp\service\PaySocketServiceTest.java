package com.psbc.cpufp.service;

import com.psbc.cpufp.config.ZhbgProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.anyString;

@ExtendWith(MockitoExtension.class)
class PaySocketServiceTest {

    @Mock
    private ZhbgProperties zhbgProperties;

    @Mock
    private ClientIWebService webServiceClient;

    @InjectMocks
    private PaySocketService paySocketService;

    @BeforeEach
    void setUp() {
        when(zhbgProperties.getZhbgPostUrl()).thenReturn("http://localhost:9091/");
        when(zhbgProperties.getUrl30001()).thenReturn("api/admin/xzp/queryYxjfLsxdye");
        when(zhbgProperties.getUrl20006()).thenReturn("api/admin/xzp/queryJgzhInfo");

        // Mock WebService客户端响应
        when(webServiceClient.Execute(anyString(), anyString()))
                .thenReturn("<?xml version=\"1.0\" encoding=\"utf-8\"?><response><status>success</status></response>");
    }

    @Test
    void testProcessMessage_JsonFormat() {
        // 测试JSON格式的消息
        String jsonMessage = "{\"serviceno\":\"30001\",\"data\":\"test\"}";
        
        String result = paySocketService.processMessage(jsonMessage);
        
        assertNotNull(result);
        // 由于实际的HTTP请求可能失败，我们主要验证方法能正常执行
    }

    @Test
    void testProcessMessage_XmlFormat() {
        // 测试XML格式的消息
        String xmlMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20006</serviceno></head><body>test</body></content>";
        
        String result = paySocketService.processMessage(xmlMessage);
        
        assertNotNull(result);
    }

    @Test
    void testProcessMessage_UnknownService() {
        // 测试未知服务号的消息
        String xmlMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>99999</serviceno></head><body>test</body></content>";
        
        String result = paySocketService.processMessage(xmlMessage);
        
        assertNotNull(result);
        // 应该走委托方服务的逻辑
    }

    @Test
    void testProcessMessage_InvalidFormat() {
        // 测试无效格式的消息
        String invalidMessage = "invalid message format";
        
        String result = paySocketService.processMessage(invalidMessage);
        
        assertNotNull(result);
        // 应该走委托方服务的逻辑
    }
}
