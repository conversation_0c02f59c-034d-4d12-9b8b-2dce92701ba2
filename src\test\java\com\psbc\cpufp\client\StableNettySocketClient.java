package com.psbc.cpufp.client;

import com.psbc.cpufp.util.PortChecker;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * 稳定版Netty Socket客户端测试类
 * 增加了重试机制和更好的错误处理
 */
@Slf4j
public class StableNettySocketClient {

    private static final String SERVER_HOST = "localhost";
    private static final int SERVER_PORT = 8888;
    private static final int MAX_RETRIES = 3;
    private static final int RETRY_DELAY_MS = 1000;

    public static void main(String[] args) {
        StableNettySocketClient client = new StableNettySocketClient();
        
        // 首先检查服务器是否启动
        log.info("检查Netty服务器是否启动...");
        if (!PortChecker.isPortAvailable(SERVER_HOST, SERVER_PORT)) {
            log.error("Netty服务器未启动，请先启动Spring Boot应用");
            log.info("等待服务器启动...");
            if (!PortChecker.waitForPort(SERVER_HOST, SERVER_PORT, 30000)) {
                log.error("等待服务器启动超时，请检查服务器配置");
                return;
            }
        }
        
        log.info("服务器已启动，开始测试...");
        
        // 测试JSON格式消息（服务号30001）
        String jsonMessage = "{\"serviceno\":\"30001\",\"data\":\"test json message\"}";
        client.sendMessageWithRetry(jsonMessage, "30001接口测试");

        // 等待一段时间再发送下一个请求
        sleep(2000);

        // 测试XML格式消息（服务号20006）
        String xmlMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20006</serviceno></head>" +
                "<body><data>test xml message</data></body></content>";
        client.sendMessageWithRetry(xmlMessage, "20006接口测试");

        // 等待一段时间再发送下一个请求
        sleep(2000);

        // 测试委托方服务（其他服务号）
        String delegateMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20001</serviceno></head>" +
                "<body><data>test delegate message</data></body></content>";
        client.sendMessageWithRetry(delegateMessage, "委托方服务测试");
        
        log.info("所有测试完成");
    }

    /**
     * 带重试机制的消息发送
     */
    public void sendMessageWithRetry(String message, String testName) {
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                log.info("开始{}，第{}次尝试", testName, attempt);
                sendMessage(message);
                log.info("{}成功完成", testName);
                return; // 成功则退出重试循环
                
            } catch (Exception e) {
                log.error("{}第{}次尝试失败: {}", testName, attempt, e.getMessage());
                
                if (attempt < MAX_RETRIES) {
                    log.info("等待{}ms后重试...", RETRY_DELAY_MS);
                    sleep(RETRY_DELAY_MS);
                } else {
                    log.error("{}在{}次尝试后仍然失败", testName, MAX_RETRIES);
                }
            }
        }
    }

    /**
     * 发送消息到服务器
     */
    public void sendMessage(String message) throws Exception {
        Socket socket = null;
        try {
            // 创建连接
            socket = createConnection();
            
            // 发送消息
            sendFormattedMessage(socket, message);
            log.info("消息发送完成，等待响应...");
            
            // 接收响应
            String response = receiveFormattedMessage(socket);
            log.info("收到响应: {}", response);
            
        } finally {
            closeConnection(socket);
        }
    }

    /**
     * 创建Socket连接
     */
    private Socket createConnection() throws IOException {
        Socket socket = new Socket(SERVER_HOST, SERVER_PORT);
        socket.setSoTimeout(30000); // 30秒读取超时
        socket.setKeepAlive(true);
        socket.setTcpNoDelay(true); // 禁用Nagle算法，立即发送数据
        
        log.info("连接到服务器: {}:{}", SERVER_HOST, SERVER_PORT);
        return socket;
    }

    /**
     * 关闭连接
     */
    private void closeConnection(Socket socket) {
        if (socket != null && !socket.isClosed()) {
            try {
                // 优雅关闭：先关闭输出流
                if (!socket.isOutputShutdown()) {
                    socket.shutdownOutput();
                }
                
                // 等待一段时间让服务器处理完成
                sleep(200);
                
                // 关闭连接
                socket.close();
                log.info("连接已关闭");
                
            } catch (Exception e) {
                log.warn("关闭连接时发生错误: {}", e.getMessage());
            }
        }
    }

    /**
     * 发送格式化的消息（添加6位长度前缀）
     */
    private void sendFormattedMessage(Socket socket, String message) throws IOException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;
        
        // 格式化长度字段为6位字符串
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);
        
        log.info("发送消息，长度: {}, 内容: {}", messageLength, message);
        
        OutputStream out = socket.getOutputStream();
        
        // 发送长度字段
        out.write(lengthBytes);
        out.flush();
        log.debug("长度字段发送完成: {}", lengthStr);
        
        // 短暂等待确保长度字段发送完成
        sleep(10);
        
        // 发送消息体
        out.write(messageBytes);
        out.flush();
        log.debug("消息体发送完成");
        
        // 等待确保数据发送完成
        sleep(100);
    }

    /**
     * 接收格式化的消息（解析6位长度前缀）
     */
    private String receiveFormattedMessage(Socket socket) throws IOException {
        InputStream in = socket.getInputStream();
        
        // 读取长度字段（6字节）
        byte[] lengthBytes = readExactBytes(in, 6, "长度字段");
        
        String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
        int messageLength;
        try {
            messageLength = Integer.parseInt(lengthStr);
            log.info("响应消息长度: {}", messageLength);
        } catch (NumberFormatException e) {
            throw new IOException("无法解析长度字段: " + lengthStr);
        }
        
        if (messageLength <= 0 || messageLength > 10 * 1024 * 1024) { // 限制最大10MB
            throw new IOException("消息长度异常: " + messageLength);
        }
        
        // 读取消息体
        byte[] messageBytes = readExactBytes(in, messageLength, "消息体");
        
        String result = new String(messageBytes, StandardCharsets.UTF_8);
        log.info("成功接收完整消息，长度: {}", result.length());
        return result;
    }

    /**
     * 精确读取指定字节数
     */
    private byte[] readExactBytes(InputStream in, int length, String description) throws IOException {
        byte[] buffer = new byte[length];
        int totalBytesRead = 0;
        
        while (totalBytesRead < length) {
            int bytesRead = in.read(buffer, totalBytesRead, length - totalBytesRead);
            if (bytesRead == -1) {
                throw new IOException("连接在读取" + description + "时关闭，已读取: " + totalBytesRead + "/" + length);
            }
            totalBytesRead += bytesRead;
            
            // 如果没有读取到数据，短暂等待
            if (bytesRead == 0) {
                sleep(10);
            }
        }
        
        log.debug("成功读取{}: {} 字节", description, totalBytesRead);
        return buffer;
    }

    /**
     * 安全的睡眠方法
     */
    private static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("睡眠被中断");
        }
    }
}
