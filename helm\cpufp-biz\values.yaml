nameSpace: cpufp-biz
appreplicaCount: 1
release: "1.0"

labels:
  env: dev

image:
  pullPolicy: Always
  imageName: cpufp.com/cpufp-biz
  tag: "1.0.2"

# 应用配置 start
comm:
  common:
    cpufpSysCode: "99711940000"
    cpufpSystemNo7: "3531003"
    cpufpSystemNo11: "35310030000"
    jasyptPassword: $jasyptPassword
  dbConfig:
    one_url: "**************************************************************************"
    one_username: "cpufpplt"
    one_password: "Tyqz@xm592"
    two_url: "**************************************************************************"
    two_username: "cpufpplt"
    two_password: "Tyqz@xm592"
  redis:
    # 模式: cluster-集群, standalone-单机
    # mode: "cluster"
    # host: "*************:6379,*************:6379,*************:6379,*************:6379,*************:6379,*************:6379"
    # port: "6379"
    # password: "ENC(62842ac88c69762520f6808fed4a2b2b)"
    # 模式:  standalone-单机 ,分行测试环境
    mode: "standalone"
    host: "************:6379"
    port: "6379"
    password: "ENC(62842ac88c69762520f6808fed4a2b2b)"
  fastdfs:
    trackerList: "************:22122,************:22122"
  health:
    endpoints: "prometheus,health"
    
branchAgent:
  soma_id: "EIDafc597c9-55f2-11ee-b9e0-8229e439784f"
  soma_token: "033ea7c28d3e488faa03a355c1333833"
  cpufpClientSecret: "b3150546bb61fdc999102fadea7c7493aa7eea50e11285a0043b9ab8efee188e"
  
# 应用配置 end
  
restartPolicy: Always
timezone: /usr/share/zoneinfo/Asia/Shanghai

portResources:
  branchAgent: 8443

mountDir:
  hostPathType: DirectoryOrCreate
  containerLogDir:
    branchAgent: "/var/cpufp/logs/branch-agent"
  hostLogDir: 
    branchAgent: "/app/logs/branch-agent"

resources:
  limits:
    cpu: 2
    memory: "4Gi"
  requests:
    cpu: 2
    memory: "4Gi"

gatewayBranchAgent:
  servers:
    port: 8443
    protocol: HTTP
    
VirtualServiceBranchAgent:
  uri: /
  timeout: 30s
  retries:
    attempts: 2

DestinationRuleBranchAgent: 
  loadBalancer: 
      simple: ROUND_ROBIN 
  connectionPool:
    tcp:
      maxConnections: 1000
      connectTimeout: 10s
    http:
      http1MaxPendingRequests: 2
      maxRequestsPerConnection: 0
  outlierDetection:
    consecutive5xxErrors: 3
    interval: 10s
    baseEjectionTime: 10s
    maxEjectionPercent: 100
   
SmartLimiterBranchAgent:
  seconds: 1
  quota: "200"
  matchUri: /

#警银通对接接口配置
jyt:
  #请求方系统号（警银通系统号）
  reqSysCode: ************
  userCert:
    #请求方用户证书序列号(警银通)
    userCertSn: 0195f5836b64
    #请求方证书公钥(警银通)
    userCertPublicKey: 04a9ab30feee114284a6155b30eae8426c794b2da2ecf15defb5f0d92c77617017d9c996574ef78666e4455e57aa86f2532ebc74a90d00e6c1cdafc646a8a108ea
    #请求方证书私钥(警银通)
    userCertPrivateKey: AMzV2Qo1EQBpFNCC1UFoMwopQ8kyTI23DxWuDHV60EB7
  #分行前置
  bankCert:
    #银行证书公钥(分行前置)
    bankCertPublicKey: 04a9ab30feee114284a6155b30eae8426c794b2da2ecf15defb5f0d92c77617017d9c996574ef78666e4455e57aa86f2532ebc74a90d00e6c1cdafc646a8a108ea
    #银行证书序列号(分行前置)
    bankCertSn: 0195f5836b64
  #公安公钥
  gongAn:
    gaCertPublicKey: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAENWHYBmioTIPpAZFdccHqwgCDG2nTRtea/VkhPUOeBB725+IvI9rsKclWkObCRC1dadxYoF+wL+RXQrpVkWmnMA==
  bankUrl:
    #卡开户信息接口
    601087Url: "http://************:9902/gw/v1/trans/J601087"
    #反欺诈查询客户名下用户接口
    600368Url: "http://************:9902/gw/v1/trans/J600368"
    #止付交易接口
    600010Url: "http://************:9902/gw/v1/trans/J600010"