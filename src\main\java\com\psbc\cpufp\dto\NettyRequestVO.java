package com.psbc.cpufp.dto;

import lombok.Data;

/**
 * Netty服务请求VO类
 * 用于接收HTTP请求并转发到8888端口的Netty服务
 */
@Data
public class NettyRequestVO {
    
    /**
     * 服务号
     */
    private String serviceno;
    
    /**
     * 用户名
     */
    private String usr;
    
    /**
     * 密码
     */
    private String pwd;
    
    /**
     * 操作员名称
     */
    private String optname;
    
    /**
     * 签名信息
     */
    private String signmsg;
    
    /**
     * 序列号
     */
    private String serialno;
    
    /**
     * 银行ID
     */
    private String bankid;
    
    /**
     * 账户名称
     */
    private String accountname;
    
    /**
     * 账户号码
     */
    private String accountno;
    
    /**
     * 执行类型
     */
    private String executetype;
    
    /**
     * 执行金额
     */
    private String executeamount;
    
    /**
     * 执行部门
     */
    private String executedept;
    
    /**
     * 执行日期
     */
    private String executedate;
    
    /**
     * 释放序列号
     */
    private String releaseserialno;
    
    /**
     * 释放时间
     */
    private String releasetime;
    
    /**
     * 备注
     */
    private String note;
    
    /**
     * 转换为XML格式
     * @return XML字符串
     */
    public String toXml() {
        StringBuilder xml = new StringBuilder();
        xml.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        xml.append("<content>");
        xml.append("<head>");
        xml.append("<serviceno>").append(serviceno != null ? serviceno : "").append("</serviceno>");
        xml.append("<usr>").append(usr != null ? usr : "").append("</usr>");
        xml.append("<pwd>").append(pwd != null ? pwd : "").append("</pwd>");
        xml.append("<optname>").append(optname != null ? optname : "").append("</optname>");
        xml.append("<signmsg>").append(signmsg != null ? signmsg : "").append("</signmsg>");
        xml.append("</head>");
        xml.append("<body>");
        xml.append("<serialno>").append(serialno != null ? serialno : "").append("</serialno>");
        xml.append("<bankid>").append(bankid != null ? bankid : "").append("</bankid>");
        xml.append("<accountname>").append(accountname != null ? accountname : "").append("</accountname>");
        xml.append("<accountno>").append(accountno != null ? accountno : "").append("</accountno>");
        xml.append("<executetype>").append(executetype != null ? executetype : "").append("</executetype>");
        xml.append("<executeamount>").append(executeamount != null ? executeamount : "").append("</executeamount>");
        xml.append("<executedept>").append(executedept != null ? executedept : "").append("</executedept>");
        xml.append("<executedate>").append(executedate != null ? executedate : "").append("</executedate>");
        xml.append("<releaseserialno>").append(releaseserialno != null ? releaseserialno : "").append("</releaseserialno>");
        xml.append("<releasetime>").append(releasetime != null ? releasetime : "").append("</releasetime>");
        xml.append("<note>").append(note != null ? note : "").append("</note>");
        xml.append("</body>");
        xml.append("</content>");
        return xml.toString();
    }
}