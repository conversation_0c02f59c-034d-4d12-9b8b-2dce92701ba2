<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务转发服务测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .health-btn {
            background-color: #27ae60;
        }
        .health-btn:hover {
            background-color: #229954;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #e2e3e5;
            border: 1px solid #d6d8db;
            color: #383d41;
        }
        .url-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>业务转发服务测试页面</h1>
        
        <div class="section">
            <h3>服务信息</h3>
            <div class="url-info">
                <strong>业务转发服务地址：</strong>http://127.0.0.1:8446<br>
                <strong>目标服务地址：</strong>http://127.0.0.1:12358<br>
                <strong>Spring Boot应用：</strong>https://127.0.0.1:8443
            </div>
        </div>

        <div class="section">
            <h3>健康检查</h3>
            <button class="health-btn" onclick="checkHealth('forwarding')">转发服务健康检查</button>
            <button class="health-btn" onclick="checkHealth('business')">业务服务健康检查</button>
            <button class="health-btn" onclick="checkHealth('test')">测试服务健康检查</button>
        </div>

        <div class="section">
            <h3>业务接口测试</h3>
            <div class="form-group">
                <label for="method">选择方法：</label>
                <select id="method">
                    <option value="list">list - 列表查询</option>
                    <option value="list1">list1 - 列表查询1</option>
                    <option value="ordin">ordin - 普通接口请求</option>
                    <option value="ordin1">ordin1 - 普通接口请求1</option>
                    <option value="ordin2">ordin2 - 普通接口请求2</option>
                </select>
            </div>
            <div class="form-group">
                <label for="requestData">请求数据 (JSON格式)：</label>
                <textarea id="requestData" placeholder='{
  "pageNum": 1,
  "pageSize": 10,
  "queryParams": {
    "status": "active"
  }
}'></textarea>
            </div>
            <button onclick="sendBusinessRequest()">发送业务请求</button>
            <button onclick="loadSampleData()">加载示例数据</button>
        </div>

        <div class="section">
            <h3>快速测试</h3>
            <button onclick="quickTest('list')">测试 List</button>
            <button onclick="quickTest('ordin')">测试 Ordin</button>
            <button onclick="quickTest('list1')">测试 List1</button>
            <button onclick="quickTest('ordin1')">测试 Ordin1</button>
            <button onclick="quickTest('ordin2')">测试 Ordin2</button>
        </div>

        <div class="section">
            <h3>响应结果</h3>
            <div id="response" class="response info">等待请求...</div>
        </div>
    </div>

    <script>
        // 健康检查
        async function checkHealth(type) {
            let url;
            switch(type) {
                case 'forwarding':
                    url = 'http://127.0.0.1:8446/health';
                    break;
                case 'business':
                    url = 'http://127.0.0.1:8446/api/business/health';
                    break;
                case 'test':
                    url = 'https://127.0.0.1:8443/test/business/health';
                    break;
            }
            
            try {
                showResponse(`正在检查 ${type} 服务健康状态...`, 'info');
                const response = await fetch(url);
                const data = await response.json();
                showResponse(`${type} 服务健康检查结果:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResponse(`${type} 服务健康检查失败:\n${error.message}`, 'error');
            }
        }

        // 发送业务请求
        async function sendBusinessRequest() {
            const method = document.getElementById('method').value;
            const requestDataText = document.getElementById('requestData').value;
            
            let requestData;
            try {
                requestData = requestDataText ? JSON.parse(requestDataText) : {};
            } catch (error) {
                showResponse('请求数据JSON格式错误:\n' + error.message, 'error');
                return;
            }
            
            const url = `http://127.0.0.1:8446/api/business/${method}`;
            
            try {
                showResponse(`正在发送 ${method} 请求到 ${url}...`, 'info');
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                showResponse(`${method} 请求响应:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResponse(`${method} 请求失败:\n${error.message}`, 'error');
            }
        }

        // 快速测试
        async function quickTest(method) {
            const sampleData = getSampleData(method);
            const url = `http://127.0.0.1:8446/api/business/${method}`;
            
            try {
                showResponse(`正在快速测试 ${method} 方法...`, 'info');
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(sampleData)
                });
                
                const data = await response.json();
                showResponse(`${method} 快速测试结果:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResponse(`${method} 快速测试失败:\n${error.message}`, 'error');
            }
        }

        // 获取示例数据
        function getSampleData(method) {
            const samples = {
                list: {
                    pageNum: 1,
                    pageSize: 10,
                    queryParams: {
                        status: 'active',
                        type: 'list'
                    }
                },
                list1: {
                    type: 'advanced',
                    filters: {
                        date: '2024-01-01',
                        category: 'test'
                    }
                },
                ordin: {
                    action: 'query',
                    params: {
                        id: '12345',
                        type: 'ordin'
                    }
                },
                ordin1: {
                    operation: 'update',
                    data: {
                        field1: 'value1',
                        field2: 'value2'
                    }
                },
                ordin2: {
                    command: 'process',
                    parameters: {
                        mode: 'batch',
                        count: 100
                    }
                }
            };
            return samples[method] || {};
        }

        // 加载示例数据
        function loadSampleData() {
            const method = document.getElementById('method').value;
            const sampleData = getSampleData(method);
            document.getElementById('requestData').value = JSON.stringify(sampleData, null, 2);
        }

        // 显示响应
        function showResponse(message, type) {
            const responseDiv = document.getElementById('response');
            responseDiv.textContent = message;
            responseDiv.className = `response ${type}`;
        }

        // 页面加载时加载默认示例数据
        window.onload = function() {
            loadSampleData();
        };
    </script>
</body>
</html>