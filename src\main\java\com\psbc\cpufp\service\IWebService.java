package com.psbc.cpufp.service;

import org.springframework.stereotype.Service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import java.io.IOException;

@Service
@WebService(targetNamespace = "http://webservice.psbc.com/")
public interface IWebService {

    @WebMethod
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    String sayHello(@WebParam(targetNamespace = "http://webservice.psbc.com/", name = "arg0") String var1);

    /**
     * 执行银行业务
     *
     * @param bankId      银行唯一代码
     * @param inParmeter  业务报文，由报文头(head节)和报文体(body节)两部分构成，报文头为用户身份认证信息，报文体为业务数据
     * @return 返回结果
     */
    @WebMethod(operationName = "Execute", action = "http://webservice.psbc.com/Execute")
    @WebResult(targetNamespace = "http://webservice.psbc.com/")
    String execute(@WebParam(targetNamespace = "http://webservice.psbc.com/", name = "arg0") String bankId,
                   @WebParam(name = "arg1") String inParmeter) throws IOException;
}
