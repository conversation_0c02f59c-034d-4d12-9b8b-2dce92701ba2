package com.psbc.cpufp.config;

import com.psbc.cpufp.service.ClientIWebService;
import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.frontend.ClientProxy;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.cxf.transport.http.HTTPConduit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WebServiceClientConfig {

    private static final Logger log = LoggerFactory.getLogger(WebServiceClientConfig.class);

    @Value("${webservice.client.url}")
    private String serviceUrl;

    @Bean
    public ClientIWebService webServiceClient() {
        log.info("Creating WebService client for URL: {}", serviceUrl);

        JaxWsProxyFactoryBean factoryBean = new JaxWsProxyFactoryBean();
        factoryBean.setServiceClass(ClientIWebService.class);
        factoryBean.setAddress(serviceUrl);

        // 创建客户端
        ClientIWebService client = (ClientIWebService) factoryBean.create();

        // 配置 SSL（如果需要）
        if (serviceUrl != null && serviceUrl.startsWith("https://")) {
            log.info("Configuring SSL for HTTPS WebService client");
            configureSSL(client);
        }

        return client;
    }

    private void configureSSL(ClientIWebService client) {
        try {
            log.info("Configuring SSL parameters for WebService client");

            org.apache.cxf.endpoint.Client cl = ClientProxy.getClient(client);
            HTTPConduit http = (HTTPConduit) cl.getConduit();

            // 使用SSL配置辅助类创建宽松的TLS参数
            TLSClientParameters tlsParams = SSLConfigurationHelper.createLenientTLSClientParameters();

            http.setTlsClientParameters(tlsParams);

            log.info("SSL configuration completed successfully");

        } catch (Exception e) {
            log.error("Failed to configure SSL for WebService client", e);
            throw new RuntimeException("SSL configuration failed", e);
        }
    }
}