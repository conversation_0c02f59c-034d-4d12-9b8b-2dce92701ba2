# 邮储银行资金监管系统 - 分行代理服务

## 📋 项目概述

本项目是邮储银行资金监管系统的分行代理服务，作为银行、综合办公系统、外联系统之间的中转服务，提供多种接口类型用于数据交互和业务处理。

### 核心功能
- **Socket通信服务**: 基于Netty的高性能Socket服务器，支持6位长度前缀协议
- **WebService服务**: 基于CXF的SOAP WebService接口
- **HTTP转发服务**: 独立的HTTP转发代理服务
- **多系统集成**: 支持与综合办公系统、委托方系统的数据交互

## 🏗️ 系统架构

### 服务端口配置
| 服务类型 | 端口 | 管理组件 | 说明 |
|---------|------|----------|------|
| Spring Boot Web | 8443 | Spring Boot | 主Web服务和WebService |
| Netty Socket | 8888 | PayServerSocket | Socket通信服务 |
| HTTP转发 | 8445 | HttpForwardingService | HTTP请求转发代理 |

### 核心组件
- **PayServerSocket**: Netty Socket服务器，处理Socket通信
- **HttpForwardingService**: HTTP转发服务，独立的HTTP代理
- **PaySocketService**: Socket业务逻辑处理服务
- **WebServiceImpl**: WebService接口实现
- **XzpController**: HTTP REST接口控制器

## 🚀 快速开始

### 环境要求
- Java 8+
- Maven 3.6+
- Spring Boot 2.x
- PostgreSQL (可选)

### 启动服务
```bash
# 克隆项目
git clone https://gitlab.psbc.com.cn/cpufp/bfes/branch-agent.git
cd branch-agent

# 编译项目
mvn clean compile

# 启动服务
mvn spring-boot:run
```

### 验证服务状态
```bash
# 检查端口状态
netstat -ano | findstr :8443
netstat -ano | findstr :8888
netstat -ano | findstr :8445

# 检查WebService WSDL
curl http://localhost:8443/xmysfzjjg/wservices/IWebServiceService?wsdl
```

## 📡 接口说明

### 1. Socket接口 (端口: 8888)

#### 协议格式
```
[6位长度字段][消息体]
```

#### 支持的服务号
- **30001**: JSON格式，查询按揭贷款信息
- **20006**: XML格式，查询监管账户变动反馈信息
- **其他**: 委托方服务

#### 测试方法
```bash
# 使用稳定版客户端测试
mvn test-compile exec:java -Dexec.mainClass="com.psbc.cpufp.client.StableNettySocketClient"

# 使用telnet测试
telnet localhost 8888
# 输入: 000050{"serviceno":"30001","data":"test"}
```

### 2. WebService接口 (端口: 8443)

#### WSDL地址
```
http://localhost:8443/xmysfzjjg/wservices/IWebServiceService?wsdl
```

#### 主要方法
- **sayHello**: 测试方法
- **Execute**: 执行银行业务

#### 测试示例
```bash
curl -X POST http://localhost:8443/xmysfzjjg/wservices/IWebServiceService \
  -H "Content-Type: text/xml" \
  -d '<?xml version="1.0"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservice.psbc.com/">
   <soapenv:Body>
      <web:sayHello><web:arg0>Test</web:arg0></web:sayHello>
   </soapenv:Body>
</soapenv:Envelope>'
```

### 3. HTTP转发接口 (端口: 8445)

#### 可用接口
```bash
# 查询有效缴费历史信息
curl -X POST http://127.0.0.1:8445/xzp/queryYxjfLsxdye \
  -H "Content-Type: application/json" \
  -d '{"test":"data"}'

# 测试接口
curl -X GET http://127.0.0.1:8445/xzp/testGet
```

## ⚙️ 配置说明

### 应用配置 (application.yml)
```yaml
server:
  port: 8443
  servlet:
    context-path: /xmysfzjjg

xmysfzjjg:
  WG_PORT: 8888    # Netty Socket端口
  HTTP_PORT: 8445  # HTTP转发端口

zhbg:
  zhbgPostUrl: http://127.0.0.1:9091/
  url30001: api/admin/xzp/queryYxjfLsxdye
  url20006: api/admin/xzp/queryJgzhInfo
```

### 环境配置
项目支持多环境配置：
- `local`: 本地开发环境
- `dev`: 开发环境
- `master`: 生产环境

## 🧪 测试

### 快速测试脚本
```bash
# Windows用户
test_8445_port.bat
```

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
# 启动服务后运行
mvn test-compile exec:java -Dexec.mainClass="com.psbc.cpufp.client.DebugNettySocketClient"
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
mvn clean package
docker build -t branch-agent .

# 运行容器
docker run -p 8443:8443 -p 8888:8888 -p 8445:8445 branch-agent
```

### Kubernetes部署
```bash
# 使用Helm部署
helm install branch-agent ./helm/cpufp-biz
```

## 🔧 开发指南

### 项目结构
```
src/main/java/com/psbc/cpufp/
├── BranchAgentApplication.java     # 主启动类
├── config/                         # 配置类
├── controller/                     # REST控制器
├── service/                        # 业务服务
├── socket/                         # Socket服务
├── httpApplication/                # HTTP转发服务
└── util/                          # 工具类
```

### 添加新的Socket服务
1. 在 `PaySocketService` 中添加新的服务号处理逻辑
2. 更新 `parseServiceNo` 方法识别新服务号
3. 实现对应的业务处理方法
4. 添加相应的单元测试

### 添加新的HTTP接口
1. 在相应的Controller中添加新方法
2. 在 `HttpForwardingService` 中添加转发规则（如需要）
3. 更新接口文档

## 🐛 故障排除

### 常见问题
1. **端口占用**: 检查端口是否被其他程序占用
2. **连接被拒绝**: 确认服务已正常启动
3. **Socket协议错误**: 检查6位长度前缀格式
4. **WebService调用失败**: 验证WSDL可访问性

### 日志查看
```bash
# 启用详细日志
mvn spring-boot:run -Dlogging.level.com.psbc.cpufp=DEBUG
```

## 📚 相关文档

- [架构优化说明](ARCHITECTURE_OPTIMIZATION.md) - 详细的架构优化过程和效果说明
- [HTTP 8445端口使用指南](HTTP_8445_PORT_GUIDE.md) - HTTP转发服务的详细使用指南

## 📈 项目开发历程

### 架构演进过程

#### 第一阶段：基础架构搭建
- 建立Spring Boot基础框架
- 实现WebService接口（CXF）
- 集成PostgreSQL数据库
- 配置多环境支持（local/dev/master）

#### 第二阶段：Socket服务重构
- **问题**: 原有基于传统Socket + ServerThread多线程模式性能瓶颈
- **解决方案**: 重构为基于Netty的高性能异步网络框架
- **核心改进**:
  - 实现自定义编解码器（6位长度前缀协议）
  - 封装业务逻辑到PaySocketService
  - 支持JSON和XML两种消息格式
  - 异步非阻塞处理，提升并发能力

#### 第三阶段：HTTP转发服务
- **需求**: 提供独立的HTTP转发代理服务
- **实现**: 新增HttpForwardingService组件
- **特性**:
  - 独立端口（8445）运行
  - 自动转发到Spring Boot主应用
  - 支持多种HTTP方法（GET/POST）
  - 统一的接口路径管理

#### 第四阶段：架构优化
- **问题识别**: SocketListener存在重复启动和职责不清的问题
- **优化方案**:
  - 移除SocketListener，避免重复启动PayServerSocket
  - 职责分离：PayServerSocket专管Netty服务，HttpForwardingService专管HTTP转发
  - 利用Spring生命周期管理（@PostConstruct/@PreDestroy）
  - 清理不必要的@ServletComponentScan注解

#### 第五阶段：端口标准化
- **统一端口配置**: 将Spring Boot主服务端口从6666统一改为8443
- **全面更新**: 配置文件、代码、文档、Helm部署配置的一致性更新
- **测试验证**: 提供完整的测试脚本和验证方法

### 技术栈演进

| 组件 | 初始版本 | 当前版本 | 改进说明 |
|------|----------|----------|----------|
| Socket服务 | 传统Socket + 多线程 | Netty异步框架 | 性能提升，代码更清晰 |
| HTTP服务 | 单一Spring Boot | Spring Boot + 独立HTTP转发 | 服务分离，更灵活 |
| 配置管理 | 硬编码 | 多环境配置文件 | 环境适配性更强 |
| 服务启动 | 手动管理 | Spring生命周期自动管理 | 更可靠，避免重复启动 |
| 端口管理 | 分散配置 | 统一标准化 | 配置一致性更好 |

### 核心优化成果

1. **性能提升**
   - Netty异步处理提升Socket服务并发能力
   - 零拷贝和池化技术减少内存开销
   - Boss-Worker线程模型优化资源利用

2. **架构清晰**
   - 三个独立服务：Spring Boot Web (8443)、Netty Socket (8888)、HTTP转发 (8445)
   - 职责分离明确，每个组件功能单一
   - Spring生命周期统一管理，避免重复启动

3. **可维护性**
   - 代码结构清晰，业务逻辑分层
   - 配置文件标准化，支持多环境
   - 完善的文档和测试支持

4. **扩展性**
   - 易于添加新的Socket服务号
   - HTTP转发服务支持新接口扩展
   - 模块化设计便于功能增强

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目为邮储银行内部项目，版权归邮储银行所有。

## 👥 维护团队

- 开发团队: CPUFP BFES Team
- 联系方式: 通过GitLab Issues或内部沟通渠道

## 🎯 项目总结

本项目经过多个阶段的迭代优化，已发展成为一个高性能、高可用的资金监管系统分行代理服务。主要特点：

- **高性能**: 基于Netty的异步Socket服务，支持高并发处理
- **多协议**: 同时支持Socket、WebService、HTTP三种通信协议
- **高可用**: 独立的服务组件，故障隔离能力强
- **易维护**: 清晰的架构设计，完善的文档支持
- **标准化**: 统一的端口配置和环境管理

项目已成功应用于邮储银行资金监管业务，为银行、综合办公系统、外联系统之间提供稳定可靠的数据交互服务。

---

**注意**: 本项目包含银行敏感信息，请严格按照银行信息安全规范进行开发和部署。
