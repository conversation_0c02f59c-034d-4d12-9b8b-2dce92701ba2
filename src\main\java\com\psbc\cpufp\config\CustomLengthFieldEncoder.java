package com.psbc.cpufp.config;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * 自定义长度字段编码器
 * 协议格式：6位长度字段 + 消息体
 * 长度字段表示消息体的字节长度，不包含长度字段本身
 */
@Slf4j
public class CustomLengthFieldEncoder extends MessageToByteEncoder<String> {

    private static final int LENGTH_FIELD_SIZE = 6;

    @Override
    protected void encode(ChannelHandlerContext ctx, String msg, ByteBuf out) throws Exception {
        if (msg == null) {
            return;
        }

        // 将消息转换为字节数组
        byte[] messageBytes = msg.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;

        // 格式化长度字段为6位字符串（左补0）
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);

        log.info("编码消息，长度: {}, 内容: {}", messageLength, msg);

        // 写入长度字段
        out.writeBytes(lengthBytes);
        // 写入消息体
        out.writeBytes(messageBytes);
    }
}
