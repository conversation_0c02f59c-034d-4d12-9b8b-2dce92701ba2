package com.psbc.cpufp.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 业务工具类
 * 提供字符串处理、日期处理等通用功能
 */
public class BusinessUtil {
    
    /**
     * 判断字符串是否为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 判断字符串是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * 获取字符串的安全值，如果为null则返回空字符串
     */
    public static String getSafeValue(String str) {
        return str == null ? "" : str;
    }
    
    /**
     * 获取字符串的安全值，如果为null或空则返回默认值
     */
    public static String getSafeValue(String str, String defaultValue) {
        return isEmpty(str) ? defaultValue : str;
    }
    
    /**
     * 获取当前日期时间字符串
     */
    public static String getCurrentDateTime() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }
    
    /**
     * 获取指定格式的当前日期时间字符串
     */
    public static String getCurrentDateTime(String pattern) {
        return new SimpleDateFormat(pattern).format(new Date());
    }
    
    /**
     * 获取当前时间戳
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
    
    /**
     * 获取当前简短时间（yyyyMMddHHmmss）
     */
    public static String getCurrentShortTime() {
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }
    
    /**
     * 格式化日期
     */
    public static String formatDate(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(pattern).format(date);
    }
}