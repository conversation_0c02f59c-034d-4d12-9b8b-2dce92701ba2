package com.psbc.cpufp.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.Socket;

/**
 * 端口检查工具类
 */
@Slf4j
public class PortChecker {

    /**
     * 检查指定端口是否可用
     * @param host 主机地址
     * @param port 端口号
     * @return true表示端口可用（有服务监听），false表示端口不可用
     */
    public static boolean isPortAvailable(String host, int port) {
        try (Socket socket = new Socket(host, port)) {
            log.info("端口 {}:{} 可用", host, port);
            return true;
        } catch (IOException e) {
            log.warn("端口 {}:{} 不可用: {}", host, port, e.getMessage());
            return false;
        }
    }

    /**
     * 等待端口变为可用状态
     * @param host 主机地址
     * @param port 端口号
     * @param timeoutMs 超时时间（毫秒）
     * @return true表示端口在超时时间内变为可用，false表示超时
     */
    public static boolean waitForPort(String host, int port, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        long endTime = startTime + timeoutMs;
        
        while (System.currentTimeMillis() < endTime) {
            if (isPortAvailable(host, port)) {
                return true;
            }
            
            try {
                Thread.sleep(500); // 每500ms检查一次
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        log.error("等待端口 {}:{} 可用超时，超时时间: {}ms", host, port, timeoutMs);
        return false;
    }

    public static void main(String[] args) {
        // 测试端口检查功能
        String host = "localhost";
        int port = 8888;
        
        System.out.println("检查端口 " + host + ":" + port);
        boolean available = isPortAvailable(host, port);
        System.out.println("端口可用: " + available);
        
        if (!available) {
            System.out.println("等待端口变为可用...");
            boolean result = waitForPort(host, port, 30000); // 等待30秒
            System.out.println("等待结果: " + result);
        }
    }
}
