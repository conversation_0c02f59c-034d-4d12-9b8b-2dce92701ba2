package com.psbc.cpufp.integration;

import com.psbc.cpufp.service.ClientIWebService;
import com.psbc.cpufp.service.PaySocketService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebService客户端集成测试
 * 验证WebServiceClientConfig配置是否正确工作
 */
@SpringBootTest
@ActiveProfiles("test")
class WebServiceClientIntegrationTest {

    @Autowired
    private ClientIWebService webServiceClient;

    @Autowired
    private PaySocketService paySocketService;

    @Test
    void testWebServiceClientBeanCreation() {
        // 验证WebService客户端Bean是否正确创建
        assertNotNull(webServiceClient, "WebService客户端应该被正确注入");
    }

    @Test
    void testPaySocketServiceBeanCreation() {
        // 验证PaySocketService Bean是否正确创建
        assertNotNull(paySocketService, "PaySocketService应该被正确注入");
    }

    @Test
    void testWebServiceClientConfiguration() {
        // 验证WebService客户端配置是否正确
        assertNotNull(webServiceClient, "WebService客户端配置应该正确");
        
        // 可以添加更多的配置验证逻辑
        // 例如验证客户端的地址配置等
    }

    @Test
    void testPaySocketServiceIntegration() {
        // 测试PaySocketService与WebService客户端的集成
        // 注意：这里使用模拟数据，避免实际的网络调用
        
        String testMessage = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                "<content><head><serviceno>20006</serviceno></head>" +
                "<body><data>integration test</data></body></content>";
        
        // 由于实际的WebService可能不可用，这里主要验证方法调用不会抛出配置相关的异常
        assertDoesNotThrow(() -> {
            try {
                String result = paySocketService.processMessage(testMessage);
                assertNotNull(result, "处理结果不应该为null");
            } catch (Exception e) {
                // 如果是网络连接异常，这是预期的（因为测试环境可能没有实际的WebService）
                // 但不应该是配置相关的异常
                assertTrue(e.getMessage().contains("连接") || 
                          e.getMessage().contains("网络") || 
                          e.getMessage().contains("超时") ||
                          e.getMessage().contains("WebService"),
                          "应该是网络相关异常，而不是配置异常: " + e.getMessage());
            }
        }, "PaySocketService处理消息时不应该抛出配置相关异常");
    }
}
